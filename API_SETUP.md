# Local Stock Data Scraping Setup

## Overview

This application now uses **local web scraping** with **Supabase database storage** instead of expensive APIs. The system automatically scrapes Yahoo Finance every 10 minutes to keep data fresh.

## Step 1: Set Up Supabase Database

### 1.1 Access Your Supabase Project
- URL: https://fqtooantsrizxjdgtkdx.supabase.co
- Go to: **SQL Editor** → **New Query**

### 1.2 Co<PERSON> and Paste the Database Schema
Open the file `supabase-schema.sql` and copy the entire content, then paste it into the Supabase SQL Editor and run it.

This will create:
- ✅ **stocks** table (basic stock information)
- ✅ **stock_quotes** table (real-time price data)
- ✅ **stock_financials** table (financial metrics)
- ✅ **stock_analysis** table (calculated scores)
- ✅ **search_history** table (popularity tracking)
- ✅ **scraping_logs** table (monitoring)
- ✅ **25 popular stocks** pre-loaded

## Step 2: Environment Configuration

The `.env.local` file is already configured with your Supabase credentials:

```bash
NEXT_PUBLIC_SUPABASE_URL=https://fqtooantsrizxjdgtkdx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
NODE_ENV=development
```

## Step 3: Start the Application

```bash
cd value-stock-invest
bun dev
```

## Step 4: How the System Works

### 🔄 **Automatic Data Scraping**
- **Every 10 minutes**: System checks which stocks need fresh data
- **Intelligent scraping**: Only updates stocks older than 10 minutes
- **Rate limiting**: Built-in delays to avoid overwhelming Yahoo Finance
- **Error handling**: Continues working even if some stocks fail

### 📊 **Data Storage**
- **Real-time quotes**: Price, volume, market cap, P/E ratio
- **Financial metrics**: Revenue, debt, cash flow, growth rates
- **Calculated analysis**: Investment scores and recommendations
- **Search tracking**: Popular stocks based on user searches

### 🎯 **Features**
- **Fresh data display**: Shows when data was last updated
- **On-demand scraping**: Triggers scraping when you search for a stock
- **Popular stocks**: Tracks most searched stocks
- **Professional analysis**: Real investment scoring based on scraped data

## Step 5: Manual Scraping (Optional)

You can trigger manual scraping via API:

```bash
# Scrape single stock
curl -X POST http://localhost:3000/api/scrape \
  -H "Content-Type: application/json" \
  -d '{"symbol": "AAPL"}'

# Scrape multiple stocks
curl -X POST http://localhost:3000/api/scrape \
  -H "Content-Type: application/json" \
  -d '{"symbols": ["AAPL", "MSFT", "GOOGL"]}'
```

## Benefits of This Approach

✅ **No API costs** - Free web scraping
✅ **No rate limits** - Control your own scraping schedule
✅ **Fresh data** - Updates every 10 minutes automatically
✅ **Reliable** - Data stored locally in Supabase
✅ **Scalable** - Add any stock symbol you want
✅ **Professional** - Real financial analysis and scoring

## Troubleshooting

### If you see "No data available":
1. Check if the database schema was created successfully
2. Wait 10 minutes for the first scraping cycle
3. Manually trigger scraping via the API endpoint

### If scraping fails:
1. Check the `scraping_logs` table in Supabase for error details
2. Yahoo Finance might be blocking requests - the system will retry
3. Check browser console for detailed error messages

### Database Issues:
1. Verify Supabase credentials in `.env.local`
2. Check if tables were created in Supabase dashboard
3. Ensure the database schema was run successfully
