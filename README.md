# Value Stock Invest - AI-Powered Stock Analysis

A comprehensive stock analysis platform that provides real-time stock recommendations using advanced financial metrics and AI-powered scoring algorithms. Built with Next.js, TypeScript, Tailwind CSS, and integrated with Polygon.io for real-time market data.

## 🚀 Features

- **AI-Powered Stock Scoring**: Advanced algorithm that analyzes stocks based on 9 key financial metrics
- **Real-Time Stock Data**: Live price updates and financial metrics from Polygon.io API
- **Smart Update System**: Intelligent stock selection and parallel processing for optimal performance
- **Real-Time User Interactions**: Instant price updates when users search or click stocks
- **Comprehensive Financial Metrics**: P/E ratios, market cap, ROE, debt-to-equity, profit margins
- **Categorized Recommendations**:
  - **Lower Risk**: Strong financials, solid growth, trading at low prices
  - **Balanced Risk**: Strong financials, solid growth, fair to low prices
  - **Full Throttle**: Massive growth at fair prices
- **Detailed Stock Analysis**: Comprehensive breakdown of financial metrics and scoring criteria
- **Interactive Dashboard**: Modern, responsive UI with detailed stock cards and modal views
- **Risk Assessment**: Automatic risk level classification (Low, Medium, High)
- **Background Auto-Updates**: Automatic price and financial data synchronization

## 🛠️ Technology Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Headless UI, Lucide React Icons
- **Charts**: Recharts (ready for implementation)
- **Database**: Supabase (PostgreSQL)
- **Real-Time Data**: Polygon.io API (paid subscription)
- **Development**: ESLint, Turbopack

## 📊 Scoring Algorithm

Our AI-powered scoring system evaluates stocks based on:

1. **P/E Ratio** (15% weight) - Valuation metric
2. **PEG Ratio** (15% weight) - Growth-adjusted valuation
3. **Debt-to-Equity** (12% weight) - Financial stability
4. **Return on Equity** (12% weight) - Profitability efficiency
5. **Revenue Growth** (12% weight) - Business expansion
6. **Earnings Growth** (12% weight) - Profit expansion
7. **Free Cash Flow** (10% weight) - Cash generation
8. **Profit Margins** (8% weight) - Operational efficiency
9. **Current Ratio** (4% weight) - Short-term liquidity

Each stock receives a score from 1-10, with recommendations:
- **8-10**: Strong BUY
- **6-7.9**: HOLD
- **Below 6**: SELL

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd value-stock-invest
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Add your API keys to `.env.local`:
```bash
# Polygon.io API (required for real-time data)
POLYGON_API_KEY=your_polygon_api_key_here

# Supabase (required for database)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

5. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🔧 Configuration

### API Integration

The application uses real-time data from Polygon.io and Supabase:

1. **Polygon.io Setup**:
   - Sign up for a paid Polygon.io subscription
   - Get your API key from the dashboard
   - Add it to your `.env.local` file
   - The system will automatically use real-time market data

2. **Supabase Setup**:
   - Create a new Supabase project
   - Run the database schema from `supabase-schema/` directory
   - Add your Supabase URL and anon key to `.env.local`

### Performance Features

- **No Rate Limiting**: Utilizes paid Polygon.io subscription for unlimited API calls
- **Parallel Processing**: Multiple stocks updated simultaneously
- **Smart Updates**: Intelligent stock selection based on data freshness
- **Real-time Interactions**: Instant price updates on user actions

## 📱 Usage

### Stock Search
1. Use the search bar to find stocks by symbol (e.g., "AAPL") or company name (e.g., "Apple")
2. Click on popular stock buttons for quick access
3. View instant analysis results

### Stock Analysis
1. Browse categorized recommendations on the main dashboard
2. Click any stock card to view detailed analysis
3. Review scoring breakdown and financial metrics
4. Check risk assessment and investment recommendation

### Categories

- **Lower Risk**: Conservative investments with stable growth
- **Balanced Risk**: Moderate growth with reasonable risk
- **Full Throttle**: High-growth stocks with higher risk

## 🏗️ Project Structure

```
src/
├── app/
│   ├── api/stocks/         # Stock API endpoints
│   │   ├── smart-update/   # Intelligent batch updates
│   │   ├── realtime-price/ # Real-time price updates
│   │   ├── sync-financials/ # Financial metrics sync
│   │   └── popular/        # Popular stocks API
│   └── page.tsx           # Main application page
├── components/             # React components
│   ├── GoogleLikeStockSearch.tsx # Main search interface
│   ├── HorizontalStockList.tsx   # Stock display lists
│   ├── StockDetailModal.tsx      # Detailed stock view
│   ├── AutoUpdateService.tsx     # Background updates
│   └── StockChart.tsx            # Stock visualization
├── lib/                    # Core business logic
│   ├── polygonService.ts   # Polygon.io API integration
│   ├── supabase.ts        # Database operations
│   ├── stockScoring.ts    # Scoring algorithm
│   ├── recommendationEngine.ts # Recommendation logic
│   └── searchService.ts   # Search functionality
└── types/                 # TypeScript type definitions
```

## 🧪 Testing

```bash
# Run tests (when implemented)
npm test

# Run linting
npm run lint

# Type checking
npm run type-check
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms

The application can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

## ✅ Completed Features

- [x] **Real-time price updates** - Live market data from Polygon.io
- [x] **Smart update system** - Intelligent stock selection and parallel processing
- [x] **Financial metrics integration** - P/E ratios, market cap, ROE, debt ratios
- [x] **User interaction updates** - Instant price refresh on clicks/searches
- [x] **Background auto-updates** - Automatic data synchronization
- [x] **Database optimization** - Proper schema and constraints
- [x] **Performance improvements** - 15-30x faster updates
- [x] **Clean architecture** - Removed priority queue complexity

## 📈 Future Enhancements

- [ ] Portfolio tracking and management
- [ ] Historical performance charts and technical analysis
- [ ] Email alerts for stock recommendations
- [ ] Advanced filtering and sorting options
- [ ] Stock comparison tools
- [ ] Mobile app version
- [ ] User authentication and saved watchlists
- [ ] WebSocket integration for real-time updates
- [ ] Machine learning-based predictions
- [ ] Options and derivatives analysis

## ⚠️ Disclaimer

This application is for educational and informational purposes only. It should not be considered as financial advice. Always conduct your own research and consult with qualified financial advisors before making investment decisions.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support, please open an issue in the GitHub repository.
