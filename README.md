# Value Stock Invest - AI-Powered Stock Analysis

A comprehensive stock analysis platform that provides real-time stock recommendations using advanced financial metrics and AI-powered scoring algorithms. Built with Next.js, TypeScript, and Tailwind CSS.

## 🚀 Features

- **AI-Powered Stock Scoring**: Advanced algorithm that analyzes stocks based on 9 key financial metrics
- **Real-Time Stock Search**: Search for any stock by symbol or company name
- **Categorized Recommendations**:
  - **Lower Risk**: Strong financials, solid growth, trading at low prices
  - **Balanced Risk**: Strong financials, solid growth, fair to low prices
  - **Full Throttle**: Massive growth at fair prices
- **Detailed Stock Analysis**: Comprehensive breakdown of financial metrics and scoring criteria
- **Interactive Dashboard**: Modern, responsive UI with detailed stock cards and modal views
- **Risk Assessment**: Automatic risk level classification (Low, Medium, High)

## 🛠️ Technology Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Headless UI, Lucide React Icons
- **Charts**: Recharts (ready for implementation)
- **API Integration**: Yahoo Finance API (configurable)
- **Development**: ESLint, Turbopack

## 📊 Scoring Algorithm

Our AI-powered scoring system evaluates stocks based on:

1. **P/E Ratio** (15% weight) - Valuation metric
2. **PEG Ratio** (15% weight) - Growth-adjusted valuation
3. **Debt-to-Equity** (12% weight) - Financial stability
4. **Return on Equity** (12% weight) - Profitability efficiency
5. **Revenue Growth** (12% weight) - Business expansion
6. **Earnings Growth** (12% weight) - Profit expansion
7. **Free Cash Flow** (10% weight) - Cash generation
8. **Profit Margins** (8% weight) - Operational efficiency
9. **Current Ratio** (4% weight) - Short-term liquidity

Each stock receives a score from 1-10, with recommendations:
- **8-10**: Strong BUY
- **6-7.9**: HOLD
- **Below 6**: SELL

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd value-stock-invest
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. (Optional) Add your Yahoo Finance API key to `.env.local`:
```bash
NEXT_PUBLIC_RAPIDAPI_KEY=your_rapidapi_key_here
```

5. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🔧 Configuration

### API Integration

The application currently uses mock data for development. To use real stock data:

1. Sign up for a Yahoo Finance API service (e.g., RapidAPI)
2. Get your API key
3. Add it to your `.env.local` file
4. The application will automatically switch to real data

### Supported API Providers

- Yahoo Finance (via RapidAPI)
- Alpha Vantage (configurable)
- Finnhub (configurable)

## 📱 Usage

### Stock Search
1. Use the search bar to find stocks by symbol (e.g., "AAPL") or company name (e.g., "Apple")
2. Click on popular stock buttons for quick access
3. View instant analysis results

### Stock Analysis
1. Browse categorized recommendations on the main dashboard
2. Click any stock card to view detailed analysis
3. Review scoring breakdown and financial metrics
4. Check risk assessment and investment recommendation

### Categories

- **Lower Risk**: Conservative investments with stable growth
- **Balanced Risk**: Moderate growth with reasonable risk
- **Full Throttle**: High-growth stocks with higher risk

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js app directory
├── components/             # React components
│   ├── StockAnalysisDashboard.tsx
│   ├── StockSearchBar.tsx
│   ├── StockRecommendationCard.tsx
│   └── StockDetailModal.tsx
├── lib/                    # Core business logic
│   ├── yahooFinance.ts     # API integration
│   ├── stockScoring.ts     # Scoring algorithm
│   ├── recommendationEngine.ts # Recommendation logic
│   └── searchService.ts    # Search functionality
└── types/                  # TypeScript type definitions
```

## 🧪 Testing

```bash
# Run tests (when implemented)
npm test

# Run linting
npm run lint

# Type checking
npm run type-check
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms

The application can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

## 📈 Future Enhancements

- [ ] Real-time price updates
- [ ] Portfolio tracking
- [ ] Historical performance charts
- [ ] Email alerts for stock recommendations
- [ ] Advanced filtering and sorting
- [ ] Comparison tools
- [ ] Mobile app version
- [ ] User authentication and saved watchlists

## ⚠️ Disclaimer

This application is for educational and informational purposes only. It should not be considered as financial advice. Always conduct your own research and consult with qualified financial advisors before making investment decisions.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support, please open an issue in the GitHub repository.
