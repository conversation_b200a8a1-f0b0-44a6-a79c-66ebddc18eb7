# API Documentation

## Overview

The Value Stock Invest platform provides RESTful API endpoints for stock analysis and search functionality. All endpoints return JSON responses and include proper error handling.

## Base URL

```
http://localhost:3000/api
```

## Authentication

Currently, no authentication is required for API endpoints. In production, consider implementing API key authentication.

## Endpoints

### 1. Get Stock Analysis

Retrieve comprehensive analysis for a specific stock symbol.

**Endpoint:** `GET /api/stocks/{symbol}`

**Parameters:**
- `symbol` (string, required): Stock ticker symbol (e.g., "AAPL", "MSFT")

**Response:**
```json
{
  "symbol": "AAPL",
  "companyName": "Apple Inc.",
  "currentPrice": 175.84,
  "marketCap": 2800000000000,
  "score": 6.1,
  "recommendation": "HOLD",
  "riskLevel": "MEDIUM",
  "category": "Balanced Risk",
  "criteriaScores": {
    "peRatio": 7.2,
    "pegRatio": 6.8,
    "debtToEquity": 4.5,
    "returnOnEquity": 9.8,
    "revenueGrowth": 7.1,
    "earningsGrowth": 8.2,
    "freeCashflow": 8.9,
    "profitMargins": 8.5,
    "currentRatio": 5.2
  },
  "financialMetrics": {
    "peRatio": 28.5,
    "pegRatio": 2.1,
    "debtToEquity": 1.73,
    "returnOnEquity": 147.9,
    "revenueGrowth": 8.1,
    "earningsGrowth": 13.5,
    "freeCashflow": 99584000000,
    "profitMargins": 25.3,
    "currentRatio": 0.94
  },
  "lastUpdated": "2025-09-12T10:30:00.000Z"
}
```

**Error Responses:**
- `404`: Stock not found
- `500`: Internal server error

### 2. Search Stocks

Search for stocks by symbol or company name.

**Endpoint:** `GET /api/search?q={query}`

**Parameters:**
- `q` (string, required): Search query (symbol or company name)

**Response:**
```json
{
  "query": "apple",
  "results": [
    {
      "symbol": "AAPL",
      "name": "Apple Inc.",
      "type": "stock",
      "exchange": "NASDAQ",
      "analysis": {
        "symbol": "AAPL",
        "companyName": "Apple Inc.",
        "score": 6.1,
        "category": "Balanced Risk",
        "criteriaPassedCount": 2,
        "criteriaPassedPercentage": 22,
        "metrics": {
          "earningsGrowth1Yr": 13.5,
          "salesGrowth1Yr": 8.1,
          "salesGrowth5Yr": 12.15,
          "debtToEquity": 1.73,
          "freeCashFlow": "$99.58 Billion",
          "pegRatio": 2.1,
          "returnOnEquity": 147.9
        },
        "recommendation": "HOLD",
        "riskLevel": "MEDIUM",
        "currentPrice": 175.84,
        "marketCap": "$2.80 Trillion"
      },
      "price": 175.84
    }
  ],
  "count": 1,
  "timestamp": "2025-09-12T10:30:00.000Z"
}
```

**Error Responses:**
- `400`: Missing query parameter
- `500`: Internal server error

## Data Models

### Stock Analysis Response

```typescript
interface StockAnalysisResponse {
  symbol: string;
  companyName: string;
  currentPrice: number;
  marketCap: number;
  score: number;
  recommendation: 'BUY' | 'HOLD' | 'SELL';
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  category: 'Lower Risk' | 'Balanced Risk' | 'Full Throttle';
  criteriaScores: {
    peRatio: number;
    pegRatio: number;
    debtToEquity: number;
    returnOnEquity: number;
    revenueGrowth: number;
    earningsGrowth: number;
    freeCashflow: number;
    profitMargins: number;
    currentRatio: number;
  };
  financialMetrics: {
    peRatio: number;
    pegRatio: number;
    debtToEquity: number;
    returnOnEquity: number;
    revenueGrowth: number;
    earningsGrowth: number;
    freeCashflow: number;
    profitMargins: number;
    currentRatio: number;
  };
  lastUpdated: string;
}
```

### Search Result

```typescript
interface SearchResult {
  symbol: string;
  name: string;
  type: 'stock';
  exchange: string;
  analysis: StockRecommendation;
  price: number;
}

interface SearchResponse {
  query: string;
  results: SearchResult[];
  count: number;
  timestamp: string;
}
```

## Rate Limiting

Currently, no rate limiting is implemented. In production, consider implementing:
- Rate limiting per IP address
- API key-based quotas
- Caching to reduce API calls

## Error Handling

All endpoints return consistent error responses:

```json
{
  "error": "Error message description",
  "code": "ERROR_CODE",
  "timestamp": "2025-09-12T10:30:00.000Z"
}
```

## CORS

CORS is enabled for development. In production, configure appropriate origins:

```javascript
headers: {
  'Access-Control-Allow-Origin': 'https://yourdomain.com',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
}
```

## Examples

### cURL Examples

**Get stock analysis:**
```bash
curl -X GET "http://localhost:3000/api/stocks/AAPL" \
  -H "Content-Type: application/json"
```

**Search stocks:**
```bash
curl -X GET "http://localhost:3000/api/search?q=apple" \
  -H "Content-Type: application/json"
```

### JavaScript Examples

**Using fetch:**
```javascript
// Get stock analysis
const response = await fetch('/api/stocks/AAPL');
const stockData = await response.json();

// Search stocks
const searchResponse = await fetch('/api/search?q=apple');
const searchResults = await searchResponse.json();
```

**Using axios:**
```javascript
import axios from 'axios';

// Get stock analysis
const stockData = await axios.get('/api/stocks/AAPL');

// Search stocks
const searchResults = await axios.get('/api/search', {
  params: { q: 'apple' }
});
```

## Data Sources

### Yahoo Finance API
- Real-time stock quotes
- Financial statements
- Key statistics
- Company information

### Mock Data
When Yahoo Finance API is unavailable, the system uses mock data for:
- AAPL, MSFT, GOOGL, AMZN, NVDA, META, TSLA, BRK.B, JNJ

## Performance Considerations

- API responses are cached for 5 minutes
- Mock data is used when external API fails
- Responses include timestamps for cache validation
- Consider implementing Redis for production caching

## Security

### Current Implementation
- Input validation on all parameters
- Error message sanitization
- CORS configuration

### Production Recommendations
- Implement API authentication
- Add rate limiting
- Use HTTPS only
- Validate and sanitize all inputs
- Implement request logging
- Add API monitoring

## Monitoring

Consider implementing:
- API response time tracking
- Error rate monitoring
- Usage analytics
- Health check endpoints

## Future Enhancements

- WebSocket support for real-time updates
- Batch stock analysis endpoints
- Historical data endpoints
- Portfolio tracking APIs
- User authentication and preferences
