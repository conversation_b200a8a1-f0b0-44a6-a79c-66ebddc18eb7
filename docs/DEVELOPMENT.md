# Development Guide

## 🚀 Getting Started

### Prerequisites

- **Bun** (recommended) - Fast JavaScript runtime and package manager
- **Node.js 18+** (alternative to Bun)
- **Git** for version control
- **VS Code** (recommended IDE)

### Installation

1. **Install Bun** (if not already installed):
   ```bash
   curl -fsSL https://bun.sh/install | bash
   ```

2. **Clone and setup project**:
   ```bash
   git clone <repository-url>
   cd value-stock-invest
   bun install
   ```

3. **Start development server**:
   ```bash
   bun dev
   ```

## 🏗️ Project Architecture

### Directory Structure

```
value-stock-invest/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API routes
│   │   │   ├── stocks/[symbol]/route.ts
│   │   │   └── search/route.ts
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # React components
│   │   ├── StockAnalysisDashboard.tsx  # Main dashboard
│   │   ├── StockSearchBar.tsx          # Search functionality
│   │   ├── StockRecommendationCard.tsx # Stock cards
│   │   ├── StockDetailModal.tsx        # Detailed analysis
│   │   └── StockChart.tsx              # Data visualization
│   └── lib/                   # Business logic & utilities
│       ├── yahooFinance.ts    # API integration
│       ├── stockScoring.ts    # Scoring algorithm
│       ├── recommendationEngine.ts     # Recommendation logic
│       └── searchService.ts   # Search functionality
├── docs/                      # Documentation
├── public/                    # Static assets
├── .env.example              # Environment template
├── package.json              # Dependencies
├── tailwind.config.ts        # Tailwind configuration
├── tsconfig.json            # TypeScript configuration
└── next.config.js           # Next.js configuration
```

### Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS + Headless UI
- **Charts**: Recharts
- **Icons**: Lucide React
- **Runtime**: Bun (recommended) or Node.js
- **API**: Yahoo Finance via RapidAPI

## 🧩 Component Architecture

### Component Hierarchy

```
App (layout.tsx)
└── StockAnalysisDashboard
    ├── StockSearchBar
    ├── StockRecommendationCard[]
    │   └── CompactStockChart
    └── StockDetailModal
        └── StockChartDashboard
            └── StockChart[]
```

### Component Guidelines

1. **Functional Components**: Use function declarations with TypeScript
2. **Hooks**: Prefer built-in hooks, custom hooks for complex logic
3. **Props**: Define interfaces for all component props
4. **State Management**: Use React hooks (useState, useEffect, useRef)
5. **Error Boundaries**: Implement for production robustness

### Example Component Structure

```typescript
'use client';

import { useState, useEffect } from 'react';
import { ComponentProps } from '@/types';

interface MyComponentProps {
  data: ComponentProps;
  onAction: (id: string) => void;
}

export default function MyComponent({ data, onAction }: MyComponentProps) {
  const [state, setState] = useState<string>('');

  useEffect(() => {
    // Effect logic
  }, []);

  return (
    <div className="component-container">
      {/* Component JSX */}
    </div>
  );
}
```

## 📊 Data Flow

### API Integration Flow

1. **User Input** → Search/Stock Selection
2. **Frontend** → API Call to `/api/stocks/{symbol}` or `/api/search`
3. **API Route** → Yahoo Finance API (with fallback to mock data)
4. **Data Processing** → Scoring algorithm application
5. **Response** → Formatted data back to frontend
6. **UI Update** → Component re-render with new data

### State Management

- **Local State**: Component-level state with useState
- **Shared State**: Props drilling for simple cases
- **API State**: Direct API calls with loading/error states
- **Future**: Consider Zustand or Context for complex state

## 🎨 Styling Guidelines

### Tailwind CSS Conventions

1. **Responsive Design**: Mobile-first approach
   ```tsx
   className="text-sm md:text-base lg:text-lg"
   ```

2. **Color Scheme**: Consistent color palette
   ```tsx
   // Primary: Blue (blue-600, blue-500, blue-400)
   // Success: Green (green-600, green-500, green-400)
   // Warning: Yellow (yellow-600, yellow-500, yellow-400)
   // Error: Red (red-600, red-500, red-400)
   // Neutral: Gray (gray-900, gray-600, gray-400, gray-100)
   ```

3. **Spacing**: Consistent spacing scale
   ```tsx
   className="p-4 m-2 space-y-4 gap-6"
   ```

4. **Typography**: Semantic font sizes
   ```tsx
   // Headings: text-3xl, text-2xl, text-xl, text-lg
   // Body: text-base, text-sm
   // Captions: text-xs
   ```

### Component Styling Patterns

```tsx
// Card pattern
<div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow">

// Button pattern
<button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">

// Input pattern
<input className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
```

## 🔧 Development Workflow

### Daily Development

1. **Start development server**:
   ```bash
   bun dev
   ```

2. **Run type checking**:
   ```bash
   bun run type-check
   ```

3. **Lint code**:
   ```bash
   bun run lint
   ```

4. **Build for production**:
   ```bash
   bun run build
   ```

### Code Quality

- **TypeScript**: Strict mode enabled, no `any` types
- **ESLint**: Configured for Next.js and React best practices
- **Prettier**: Automatic code formatting
- **Git Hooks**: Pre-commit linting and type checking

### Testing Strategy

```bash
# Manual testing
bun dev
# Navigate to http://localhost:3000
# Test all features manually

# API testing
curl -X GET "http://localhost:3000/api/stocks/AAPL"
curl -X GET "http://localhost:3000/api/search?q=apple"

# Build testing
bun run build
bun start
```

## 🔌 API Development

### Adding New Endpoints

1. **Create route file**: `src/app/api/[endpoint]/route.ts`
2. **Implement handlers**: GET, POST, PUT, DELETE as needed
3. **Add TypeScript interfaces**: Define request/response types
4. **Error handling**: Consistent error responses
5. **Documentation**: Update API.md

### Example API Route

```typescript
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Implementation
    return NextResponse.json({ data: 'response' });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## 🧪 Testing Guidelines

### Manual Testing Checklist

- [ ] Search functionality works
- [ ] Stock cards display correctly
- [ ] Modal opens and closes properly
- [ ] Charts render without errors
- [ ] Responsive design on mobile
- [ ] API endpoints return expected data
- [ ] Error states display properly
- [ ] Loading states work correctly

### Browser Testing

- **Chrome**: Primary development browser
- **Firefox**: Secondary testing
- **Safari**: macOS compatibility
- **Mobile**: iOS Safari, Chrome Mobile

## 🚀 Deployment

### Build Process

```bash
# Production build
bun run build

# Start production server
bun start

# Environment variables
cp .env.example .env.local
# Edit .env.local with production values
```

### Environment Variables

```env
# Required for production
NEXT_PUBLIC_RAPIDAPI_KEY=your_api_key
NEXT_PUBLIC_RAPIDAPI_HOST=yahoo-finance15.p.rapidapi.com

# Optional
NODE_ENV=production
```

### Deployment Platforms

1. **Vercel** (Recommended):
   ```bash
   npm i -g vercel
   vercel
   ```

2. **Netlify**:
   ```bash
   npm run build
   # Upload dist folder
   ```

3. **Railway**:
   ```bash
   # Connect GitHub repository
   # Auto-deploy on push
   ```

## 🐛 Debugging

### Common Issues

1. **API Errors**: Check network tab, verify API keys
2. **Build Errors**: Check TypeScript errors, missing dependencies
3. **Styling Issues**: Verify Tailwind classes, check responsive design
4. **Performance**: Use React DevTools, check bundle size

### Debug Tools

- **React DevTools**: Component inspection
- **Network Tab**: API call monitoring
- **Console**: Error logging and debugging
- **Lighthouse**: Performance auditing

## 📈 Performance Optimization

### Best Practices

1. **Code Splitting**: Automatic with Next.js
2. **Image Optimization**: Use Next.js Image component
3. **Bundle Analysis**: `bun run analyze`
4. **Caching**: API response caching
5. **Lazy Loading**: Component-level lazy loading

### Monitoring

- Bundle size tracking
- API response times
- Core Web Vitals
- Error rate monitoring

## 🤝 Contributing

### Pull Request Process

1. Fork the repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Make changes with proper TypeScript types
4. Test thoroughly
5. Update documentation
6. Submit pull request

### Code Review Checklist

- [ ] TypeScript types are correct
- [ ] Components follow established patterns
- [ ] Responsive design implemented
- [ ] Error handling included
- [ ] Documentation updated
- [ ] No console errors
- [ ] Performance considerations addressed

---

**Remember**: Always use Bun for package management and development for optimal performance!
