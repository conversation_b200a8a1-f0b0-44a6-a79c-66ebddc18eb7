# Value Stock Invest - Documentation

## 📋 Project Overview

Value Stock Invest is an AI-powered stock analysis platform that provides real-time stock recommendations using comprehensive financial metrics and advanced scoring algorithms. The platform categorizes stocks into risk levels and provides detailed analysis similar to professional investment platforms.

## 🚀 Quick Start

### Prerequisites
- **Bun** (recommended) or Node.js 18+
- Git

### Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd value-stock-invest
   ```

2. **Install dependencies with Bun**
   ```bash
   bun install
   ```

3. **Start the development server**
   ```bash
   bun dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

### Environment Configuration

Create a `.env.local` file in the root directory:

```env
# Yahoo Finance API (Optional - falls back to mock data)
NEXT_PUBLIC_RAPIDAPI_KEY=your_rapidapi_key_here
NEXT_PUBLIC_RAPIDAPI_HOST=yahoo-finance15.p.rapidapi.com
```

## 🏗️ Architecture

### Tech Stack
- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS, Headless UI
- **Charts**: Recharts
- **Icons**: Lucide React
- **API**: Yahoo Finance (with mock data fallback)
- **Runtime**: Bun (recommended) or Node.js

### Project Structure
```
value-stock-invest/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API routes
│   │   │   ├── stocks/        # Stock data endpoints
│   │   │   └── search/        # Search endpoints
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # React components
│   │   ├── StockAnalysisDashboard.tsx
│   │   ├── StockSearchBar.tsx
│   │   ├── StockRecommendationCard.tsx
│   │   ├── StockDetailModal.tsx
│   │   └── StockChart.tsx
│   └── lib/                   # Business logic
│       ├── yahooFinance.ts    # API integration
│       ├── stockScoring.ts    # Scoring algorithm
│       ├── recommendationEngine.ts
│       └── searchService.ts
├── docs/                      # Documentation
└── public/                    # Static assets
```

## 🤖 AI Scoring Algorithm

### Scoring Methodology
The platform uses a weighted scoring system based on 9 key financial metrics:

| Metric | Weight | Description |
|--------|--------|-------------|
| P/E Ratio | 15% | Price-to-earnings valuation |
| PEG Ratio | 15% | Growth-adjusted valuation |
| Debt-to-Equity | 12% | Financial stability |
| Return on Equity | 12% | Profitability efficiency |
| Revenue Growth | 12% | Business expansion |
| Earnings Growth | 12% | Profit growth |
| Free Cash Flow | 10% | Cash generation |
| Profit Margins | 8% | Operational efficiency |
| Current Ratio | 4% | Liquidity |

### Risk Categories
- **Lower Risk**: Conservative, stable investments (Score 7-10)
- **Balanced Risk**: Moderate growth opportunities (Score 5-7)
- **Full Throttle**: High-growth, higher-risk stocks (Score 3-5)

### Recommendations
- **BUY**: Score ≥ 7
- **HOLD**: Score 4-7
- **SELL**: Score < 4

## 📊 Data Visualization

The platform includes comprehensive charts and visualizations:

- **Metrics Chart**: Bar chart comparing current vs. target metrics
- **Risk Assessment**: Radar chart showing multi-dimensional risk analysis
- **Score Breakdown**: Pie chart of score components
- **Performance Trends**: Line chart of historical performance
- **Compact Charts**: Mini charts for stock cards

## 🔌 API Integration

### Yahoo Finance API
The platform integrates with Yahoo Finance API through RapidAPI:

- **Stock Quotes**: Real-time price data
- **Financial Data**: Balance sheet, income statement metrics
- **Search**: Company and symbol search
- **Key Statistics**: Advanced financial ratios

### Mock Data Fallback
When API is unavailable, the system uses comprehensive mock data for:
- 9 sample stocks across all risk categories
- Realistic financial metrics
- Complete company information

## 🎨 UI/UX Features

### Design System
- **Modern Interface**: Clean, professional design
- **Responsive Layout**: Mobile-first approach
- **Interactive Elements**: Hover effects, transitions
- **Accessibility**: ARIA labels, keyboard navigation

### Key Components
- **Search Bar**: Auto-complete with popular stocks
- **Stock Cards**: Compact metric display with mini charts
- **Detail Modal**: Comprehensive analysis view
- **Dashboard**: Categorized recommendations

## 🧪 Development Guidelines

### Code Standards
- **TypeScript**: Strict type checking enabled
- **ESLint**: Code quality enforcement
- **Component Structure**: Functional components with hooks
- **File Organization**: Feature-based structure

### Best Practices
- Use Bun for package management and development
- Implement proper error handling
- Follow React best practices
- Maintain responsive design
- Use semantic HTML

### Testing
```bash
# Run development server
bun dev

# Build for production
bun run build

# Start production server
bun start
```

## 🔧 Configuration

### Next.js Configuration
The project uses Next.js 15 with:
- App Router
- Turbopack for fast development
- TypeScript support
- Tailwind CSS integration

### Bun Configuration
Bun is the recommended runtime for:
- Faster package installation
- Improved development performance
- Better TypeScript support
- Native bundling capabilities

## 📈 Performance

### Optimization Features
- **Server-Side Rendering**: Fast initial page loads
- **Code Splitting**: Automatic bundle optimization
- **Image Optimization**: Next.js image component
- **Caching**: API response caching
- **Lazy Loading**: Component-level lazy loading

### Monitoring
- Real-time error tracking
- Performance metrics
- API response times
- User interaction analytics

## 🚀 Deployment

### Production Build
```bash
bun run build
bun start
```

### Environment Variables
Set the following in production:
- `NEXT_PUBLIC_RAPIDAPI_KEY`: Yahoo Finance API key
- `NEXT_PUBLIC_RAPIDAPI_HOST`: API host URL

### Hosting Recommendations
- **Vercel**: Optimal for Next.js applications
- **Netlify**: Good alternative with edge functions
- **Railway**: Simple deployment with Bun support
- **Docker**: Containerized deployment option

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make changes with proper testing
4. Submit a pull request
5. Ensure all checks pass

### AI Assistant Guidelines
When working with this project:
- Always use Bun for package management
- Follow the established architecture patterns
- Maintain TypeScript strict mode
- Test changes thoroughly
- Update documentation as needed

## 📞 Support

For questions or issues:
- Check the documentation first
- Review existing issues
- Create detailed bug reports
- Include environment information
- Provide reproduction steps

---

**Note**: This project is designed to work seamlessly with AI assistants. The codebase follows clear patterns and includes comprehensive mock data for development without external dependencies.
