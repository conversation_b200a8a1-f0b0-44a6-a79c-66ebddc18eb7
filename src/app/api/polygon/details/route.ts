import { NextRequest, NextResponse } from 'next/server';
import { polygonService } from '@/lib/polygonService';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol');

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol parameter is required' },
        { status: 400 }
      );
    }

    const details = await polygonService.getTickerDetails(symbol.toUpperCase());

    if (!details) {
      return NextResponse.json(
        { error: `No details found for symbol: ${symbol}` },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: details,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Polygon details API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch ticker details' },
      { status: 500 }
    );
  }
}
