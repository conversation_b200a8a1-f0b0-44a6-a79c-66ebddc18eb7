import { NextRequest, NextResponse } from 'next/server';
import { polygonService } from '@/lib/polygonService';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  let upperSymbol = 'UNKNOWN';

  try {
    const { symbol } = await request.json();

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol is required' },
        { status: 400 }
      );
    }

    upperSymbol = symbol.toUpperCase();
    console.log(`Starting sync for ${upperSymbol}...`);

    // Fetch data from Polygon.io - focus on details first, snapshot is optional
    const details = await polygonService.getTickerDetails(upperSymbol);

    if (!details) {
      return NextResponse.json(
        { error: `No company details found for symbol: ${symbol}` },
        { status: 404 }
      );
    }

    // Try to get snapshot data, but don't fail if it's not available
    let snapshot = null;
    try {
      snapshot = await polygonService.getTickerSnapshot(upperSymbol);
    } catch (error) {
      console.log(`Snapshot not available for ${upperSymbol}:`, error instanceof Error ? error.message : 'Unknown error');
    }

    console.log(`Syncing ${upperSymbol} - Details: ✓, Snapshot: ${snapshot ? '✓' : '✗'}`);

    // Prepare stock data
    const stockData = {
      symbol: upperSymbol,
      name: details.name || upperSymbol,
      sector: details.sic_description || 'Unknown',
      industry: details.sic_description || 'Unknown',
      market_cap: details.market_cap || null,
      description: details.description || null,
      website: details.homepage_url || null,
      employees: details.total_employees || null,
      exchange: details.primary_exchange || 'Unknown',
      currency: details.currency_name || 'USD',
      country: 'US',
      updated_at: new Date().toISOString()
    };

    // Prepare quote data (use snapshot if available, otherwise default values)
    const quoteData = {
      symbol: upperSymbol,
      price: snapshot?.day?.c || snapshot?.prevDay?.c || 0,
      change: snapshot?.todaysChange || 0,
      change_percent: snapshot?.todaysChangePerc || 0,
      volume: snapshot?.day?.v || snapshot?.prevDay?.v || 0,
      high: snapshot?.day?.h || snapshot?.prevDay?.h || 0,
      low: snapshot?.day?.l || snapshot?.prevDay?.l || 0,
      open: snapshot?.day?.o || snapshot?.prevDay?.o || 0,
      previous_close: snapshot?.prevDay?.c || 0,
      market_cap: details.market_cap || null,
      updated_at: new Date().toISOString()
    };

    // Prepare financial data
    const financialData = {
      symbol: upperSymbol,
      market_cap: details.market_cap || null,
      shares_outstanding: details.share_class_shares_outstanding || null,
      updated_at: new Date().toISOString()
    };

    // Upsert to database
    const { error: stockError } = await supabase
      .from('stocks')
      .upsert(stockData, { onConflict: 'symbol' });

    if (stockError) {
      console.error('Error upserting stock:', stockError);
      throw stockError;
    }

    const { error: quoteError } = await supabase
      .from('stock_quotes')
      .upsert(quoteData, { onConflict: 'symbol' });

    if (quoteError) {
      console.error('Error upserting quote:', quoteError);
      throw quoteError;
    }

    const { error: financialError } = await supabase
      .from('stock_financials')
      .upsert(financialData, { onConflict: 'symbol' });

    if (financialError) {
      console.error('Error upserting financials:', financialError);
      throw financialError;
    }

    // Log activity
    await supabase
      .from('scrape_activity')
      .insert({
        symbol: upperSymbol,
        status: 'success',
        data_source: 'polygon',
        records_updated: 3,
        created_at: new Date().toISOString()
      });

    console.log(`Successfully synced ${upperSymbol}`);

    return NextResponse.json({
      success: true,
      symbol: upperSymbol,
      data: {
        stock: stockData,
        quote: quoteData,
        financials: financialData
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Polygon sync API error:', error);

    // Log failed activity
    try {
      await supabase
        .from('scrape_activity')
        .insert({
          symbol: upperSymbol,
          status: 'failed',
          data_source: 'polygon',
          error_message: error instanceof Error ? error.message : 'Unknown error',
          created_at: new Date().toISOString()
        });
    } catch (logError) {
      console.error('Error logging failed activity:', logError);
    }

    return NextResponse.json(
      {
        error: 'Failed to sync stock data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
