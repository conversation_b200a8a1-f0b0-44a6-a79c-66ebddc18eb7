import { NextRequest, NextResponse } from 'next/server';
import { SearchService } from '@/lib/searchService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { symbol } = body;

    if (!symbol || typeof symbol !== 'string') {
      return NextResponse.json(
        { error: 'Symbol is required and must be a string' },
        { status: 400 }
      );
    }

    // Increment the stock popularity
    await SearchService.incrementStockPopularity(symbol);

    // Get updated popularity
    const popularity = SearchService.getStockPopularity(symbol);

    return NextResponse.json({
      symbol: symbol.toUpperCase(),
      popularity,
      message: 'Popularity incremented successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error incrementing stock popularity:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol');
    const limit = parseInt(searchParams.get('limit') || '10');

    if (symbol) {
      // Get popularity for specific symbol
      const popularity = SearchService.getStockPopularity(symbol);
      return NextResponse.json({
        symbol: symbol.toUpperCase(),
        popularity,
        timestamp: new Date().toISOString()
      });
    } else {
      // Get top stocks by popularity
      const topStocks = SearchService.getTopStocksByPopularity(limit);
      return NextResponse.json({
        topStocks,
        count: topStocks.length,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Error getting stock popularity:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle CORS for development
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
