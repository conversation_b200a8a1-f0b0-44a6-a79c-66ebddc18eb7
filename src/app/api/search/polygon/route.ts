import { NextRequest, NextResponse } from 'next/server';
import { polygonService } from '@/lib/polygonService';
import { DatabaseService } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');

    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      );
    }

    console.log(`Searching Polygon API for: "${query}"`);

    // Search using Polygon API
    const polygonResults = await polygonService.searchTickers(query, 10);
    
    // Filter for US stocks only
    const usStocks = polygonResults.filter(ticker => 
      ticker.market === 'stocks' && 
      ticker.locale === 'us' && 
      ticker.active
    );

    const addedStocks = [];

    // Add new stocks to database
    for (const ticker of usStocks) {
      try {
        // Add stock to database if it doesn't exist
        const addedStock = await DatabaseService.addStock({
          symbol: ticker.ticker,
          name: ticker.name,
          exchange: ticker.primary_exchange,
          sector: 'Unknown', // Polygon doesn't provide sector in search
          industry: 'Unknown'
        });

        if (addedStock) {
          // Try to get additional data from Polygon for the stock, but don't fail if it doesn't work
          try {
            // Only try to get ticker details, skip snapshot for now due to API limitations
            const tickerDetails = await polygonService.getTickerDetails(ticker.ticker);

            // If we have market cap from details, we can add a basic quote
            if (tickerDetails?.market_cap) {
              await DatabaseService.addQuote({
                symbol: ticker.ticker,
                price: 0, // We don't have current price from search
                change_amount: 0,
                change_percent: 0,
                volume: 0,
                market_cap: tickerDetails.market_cap,
                currency: ticker.currency_name || 'USD',
                week_52_low: 0,
                week_52_high: 0
              });
            }

            addedStocks.push({
              symbol: ticker.ticker,
              name: ticker.name,
              exchange: ticker.primary_exchange,
              added_to_db: true
            });

            console.log(`Added new stock to database: ${ticker.ticker} - ${ticker.name}`);
          } catch (dataError) {
            console.warn(`Failed to get additional data for ${ticker.ticker}:`, dataError);
            // Still add the basic stock info even if we can't get additional data
            addedStocks.push({
              symbol: ticker.ticker,
              name: ticker.name,
              exchange: ticker.primary_exchange,
              added_to_db: true
            });
          }
        }
      } catch (dbError) {
        console.warn(`Failed to add ${ticker.ticker} to database:`, dbError);
        // Still include in results even if DB add failed
        addedStocks.push({
          symbol: ticker.ticker,
          name: ticker.name,
          exchange: ticker.primary_exchange,
          added_to_db: false
        });
      }
    }

    return NextResponse.json({
      query,
      results: addedStocks,
      count: addedStocks.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in Polygon search API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Handle CORS for development
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
