import { NextRequest, NextResponse } from 'next/server';
import { SearchService } from '@/lib/searchService';
import { YahooFinanceService } from '@/lib/yahooFinance';
import { polygonService } from '@/lib/polygonService';
import { DatabaseService } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');

    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      );
    }

    // First search in local database
    const localResults = await YahooFinanceService.searchStocks(query);
    let results = localResults.map(result => ({
      symbol: result.symbol,
      name: result.name,
      type: 'stock' as const,
      exchange: 'NASDAQ' // Default exchange
    }));

    // If no local results found, search using Polygon API
    if (results.length === 0) {
      console.log(`No local results for "${query}", searching Polygon API...`);

      try {
        const polygonResults = await polygonService.searchTickers(query, 10);

        // Filter for US stocks only
        const usStocks = polygonResults.filter(ticker =>
          ticker.market === 'stocks' &&
          ticker.locale === 'us' &&
          ticker.active
        );

        // Add new stocks to database and get price data
        for (const ticker of usStocks) {
          try {
            await DatabaseService.addStock({
              symbol: ticker.ticker,
              name: ticker.name,
              exchange: ticker.primary_exchange,
              sector: 'Unknown',
              industry: 'Unknown'
            });

            // Try to get current price data from Polygon using market cap calculation
            try {
              const priceData = await polygonService.getStockPrice(ticker.ticker);
              if (priceData) {
                await DatabaseService.addQuote({
                  symbol: ticker.ticker,
                  price: priceData.price,
                  change_amount: priceData.change,
                  change_percent: priceData.changePercent,
                  volume: priceData.volume || 0,
                  market_cap: priceData.marketCap || 0,
                  currency: ticker.currency_name || 'USD',
                  week_52_low: 0, // We don't have 52-week data from this method
                  week_52_high: 0
                });
                console.log(`Added price data for ${ticker.ticker}: $${priceData.price.toFixed(2)}`);
              } else {
                console.log(`No price data available for ${ticker.ticker}, will add placeholder`);
                // Add a basic quote with zero price if we can't get real data
                await DatabaseService.addQuote({
                  symbol: ticker.ticker,
                  price: 0,
                  change_amount: 0,
                  change_percent: 0,
                  volume: 0,
                  currency: ticker.currency_name || 'USD',
                  week_52_low: 0,
                  week_52_high: 0
                });
              }
            } catch (priceError) {
              console.warn(`Failed to get price data for ${ticker.ticker}:`, priceError);
              // Add a basic quote with zero price if we can't get real data
              await DatabaseService.addQuote({
                symbol: ticker.ticker,
                price: 0,
                change_amount: 0,
                change_percent: 0,
                volume: 0,
                currency: ticker.currency_name || 'USD',
                week_52_low: 0,
                week_52_high: 0
              });
            }

            console.log(`Added new stock to database: ${ticker.ticker} - ${ticker.name}`);

            // Add to update queue for future automatic updates
            try {
              await fetch(`${request.nextUrl.origin}/api/stocks/update-queue`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ symbol: ticker.ticker })
              });
              console.log(`Added ${ticker.ticker} to update queue`);
            } catch (queueError) {
              console.warn(`Failed to add ${ticker.ticker} to update queue:`, queueError);
            }

          } catch (dbError) {
            console.warn(`Failed to add ${ticker.ticker} to database:`, dbError);
          }
        }

        // Convert Polygon results to SearchResult format
        results = usStocks.map(ticker => ({
          symbol: ticker.ticker,
          name: ticker.name,
          type: 'stock' as const,
          exchange: ticker.primary_exchange
        }));

        console.log(`Found ${results.length} new stocks from Polygon API`);
      } catch (polygonError) {
        console.error('Polygon API search failed:', polygonError);
      }
    }

    // Use SearchService for analysis of top results
    const detailedResults = await SearchService.searchStocks(query);

    return NextResponse.json({
      query,
      results: detailedResults,
      count: detailedResults.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in search API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle CORS for development
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
