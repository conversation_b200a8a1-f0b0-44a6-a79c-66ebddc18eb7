import { NextRequest, NextResponse } from 'next/server';
import { YahooFinanceService } from '@/lib/yahooFinance';
import { StockScoringEngine } from '@/lib/stockScoring';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ symbol: string }> }
) {
  try {
    const { symbol: rawSymbol } = await params;
    const symbol = rawSymbol.toUpperCase();

    // Get real stock data from API
    const stockData = await YahooFinanceService.getStockData(symbol);

    if (!stockData) {
      return NextResponse.json(
        { error: 'Stock not found' },
        { status: 404 }
      );
    }

    // Calculate the stock score
    const stockScore = StockScoringEngine.calculateScore(stockData);

    // Return the complete analysis
    const response = {
      symbol: stockData.quote.symbol,
      companyName: stockData.quote.shortName || stockData.quote.longName,
      currentPrice: stockData.quote.regularMarketPrice,
      marketCap: stockData.quote.marketCap,
      score: stockScore.overallScore,
      recommendation: stockScore.recommendation,
      riskLevel: stockScore.riskLevel,
      category: stockScore.category,
      criteriaScores: stockScore.criteriaScores,
      financialMetrics: {
        peRatio: stockData.quote.trailingPE,
        pegRatio: stockData.keyStats.pegRatio,
        debtToEquity: stockData.financials.debtToEquity,
        returnOnEquity: stockData.financials.returnOnEquity,
        revenueGrowth: stockData.financials.revenueGrowth,
        earningsGrowth: stockData.financials.earningsGrowth,
        freeCashflow: stockData.financials.freeCashflow,
        profitMargins: stockData.financials.profitMargins,
        currentRatio: stockData.financials.currentRatio
      },
      lastUpdated: new Date().toISOString()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in stock API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle CORS for development
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
