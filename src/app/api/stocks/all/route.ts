import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { getMetricRanking } from '@/lib/metricRankings';

// GET: Fetch all stocks organized by categories
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeAnalysis = searchParams.get('analysis') === 'true';

    // Fetch all stocks from database
    const { data: stocks, error } = await supabase
      .from('stocks')
      .select('symbol, name, exchange, sector, industry, market_cap')
      .order('symbol', { ascending: true });

    if (error) {
      console.error('Error fetching stocks:', error);
      return NextResponse.json({ error: 'Failed to fetch stocks' }, { status: 500 });
    }

    if (!stocks || stocks.length === 0) {
      return NextResponse.json({
        categories: {},
        total: 0,
        timestamp: new Date().toISOString()
      });
    }

    // If analysis is requested, get latest quotes, analysis, and financials for each stock
    let stocksWithData = stocks;
    if (includeAnalysis) {
      stocksWithData = await Promise.all(
        stocks.map(async (stock) => {
          try {
            // Get latest quote
            const { data: quote } = await supabase
              .from('stock_quotes')
              .select('price, change_amount, change_percent, pe_ratio')
              .eq('symbol', stock.symbol)
              .order('scraped_at', { ascending: false })
              .limit(1)
              .single();

            // Get latest analysis
            const { data: analysis } = await supabase
              .from('stock_analysis')
              .select('overall_score, recommendation, category, risk_level')
              .eq('symbol', stock.symbol)
              .order('calculated_at', { ascending: false })
              .limit(1)
              .single();

            // Get latest financials
            const { data: financials } = await supabase
              .from('stock_financials')
              .select('return_on_equity, debt_to_equity, earnings_growth, revenue_growth, peg_ratio, profit_margins, current_ratio')
              .eq('symbol', stock.symbol)
              .order('scraped_at', { ascending: false })
              .limit(1)
              .single();

            // Calculate financial metrics with rankings
            const metrics = financials ? {
              returnOnEquity: {
                value: (financials.return_on_equity || 0) * 100, // Convert to percentage
                ranking: getMetricRanking('returnOnEquity', (financials.return_on_equity || 0) * 100)
              },
              debtToEquity: {
                value: financials.debt_to_equity || 0,
                ranking: getMetricRanking('debtToEquity', financials.debt_to_equity || 0)
              },
              earningsGrowth: {
                value: (financials.earnings_growth || 0) * 100, // Convert to percentage
                ranking: getMetricRanking('earningsGrowth', (financials.earnings_growth || 0) * 100)
              },
              salesGrowth: {
                value: (financials.revenue_growth || 0) * 100, // Convert to percentage
                ranking: getMetricRanking('salesGrowth', (financials.revenue_growth || 0) * 100)
              },
              pegRatio: {
                value: financials.peg_ratio || 0,
                ranking: getMetricRanking('pegRatio', financials.peg_ratio || 0)
              },
              profitMargins: {
                value: (financials.profit_margins || 0) * 100, // Convert to percentage
                ranking: getMetricRanking('profitMargins', (financials.profit_margins || 0) * 100)
              },
              currentRatio: {
                value: financials.current_ratio || 0,
                ranking: getMetricRanking('currentRatio', financials.current_ratio || 0)
              }
            } : null;

            return {
              ...stock,
              price: quote?.price || 0,
              change: quote?.change_amount || 0,
              changePercent: quote?.change_percent || 0,
              peRatio: quote?.pe_ratio || 0,
              score: analysis?.overall_score || 0,
              recommendation: analysis?.recommendation || 'HOLD',
              category: analysis?.category || 'Balanced Risk',
              riskLevel: analysis?.risk_level || 'MEDIUM',
              metrics
            };
          } catch (error) {
            console.warn(`Failed to get data for ${stock.symbol}:`, error);
            return {
              ...stock,
              price: 0,
              change: 0,
              changePercent: 0,
              peRatio: 0,
              score: 0,
              recommendation: 'HOLD',
              category: 'Balanced Risk',
              riskLevel: 'MEDIUM',
              metrics: null
            };
          }
        })
      );
    }

    // Organize stocks by categories
    const categories: Record<string, any[]> = {
      'Technology': [],
      'Financial Services': [],
      'Healthcare': [],
      'Consumer Discretionary': [],
      'Communication Services': [],
      'Industrials': [],
      'Energy': [],
      'Materials': [],
      'Utilities': [],
      'Real Estate': [],
      'Consumer Staples': [],
      'Other': []
    };

    // Also organize by investment categories if analysis is included
    const investmentCategories: Record<string, any[]> = {
      'Lower Risk': [],
      'Balanced Risk': [],
      'Full Throttle': []
    };

    stocksWithData.forEach((stock: any) => {
      // Organize by sector
      const sector = stock.sector || 'Other';
      if (categories[sector]) {
        categories[sector].push(stock);
      } else {
        categories['Other'].push(stock);
      }

      // Organize by investment category if analysis data is available
      if (includeAnalysis && stock.category) {
        if (investmentCategories[stock.category]) {
          investmentCategories[stock.category].push(stock);
        }
      }
    });

    // Remove empty categories
    Object.keys(categories).forEach(key => {
      if (categories[key].length === 0) {
        delete categories[key];
      }
    });

    if (includeAnalysis) {
      Object.keys(investmentCategories).forEach(key => {
        if (investmentCategories[key].length === 0) {
          delete investmentCategories[key];
        }
      });
    }

    // Sort stocks within each category by symbol
    Object.keys(categories).forEach(key => {
      categories[key].sort((a, b) => a.symbol.localeCompare(b.symbol));
    });

    if (includeAnalysis) {
      Object.keys(investmentCategories).forEach(key => {
        investmentCategories[key].sort((a, b) => b.score - a.score); // Sort by score descending
      });
    }

    const response: any = {
      sectorCategories: categories,
      total: stocks.length,
      timestamp: new Date().toISOString()
    };

    if (includeAnalysis) {
      response.investmentCategories = investmentCategories;
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in GET /api/stocks/all:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
