import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/supabase';
import { PolygonService } from '@/lib/polygonService';
import { supabase } from '@/lib/supabase';

const polygonService = new PolygonService();

// GET: Get next stocks to update based on priority
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '3');

    // Get stocks that need updating, prioritized by score and last update time
    const { data: queueData, error } = await supabase
      .from('stock_update_queue')
      .select('symbol, priority_score, last_updated_at, user_requests')
      .order('priority_score', { ascending: false })
      .order('last_updated_at', { ascending: true })
      .limit(limit);

    if (error) {
      console.error('Error fetching update queue:', error);
      return NextResponse.json({ error: 'Failed to fetch update queue' }, { status: 500 });
    }

    // If queue is empty, get some popular stocks to update
    if (!queueData || queueData.length === 0) {
      const { data: popularStocks } = await supabase
        .from('stocks')
        .select('symbol')
        .order('created_at', { ascending: false })
        .limit(limit);

      const symbols = popularStocks?.map(s => s.symbol) || ['AAPL', 'MSFT', 'GOOGL'];
      
      return NextResponse.json({
        stocks_to_update: symbols,
        source: 'popular_stocks',
        timestamp: new Date().toISOString()
      });
    }

    const stocksToUpdate = queueData.map(item => item.symbol);

    return NextResponse.json({
      stocks_to_update: stocksToUpdate,
      queue_info: queueData,
      source: 'priority_queue',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in GET /api/stocks/auto-update:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST: Execute automatic update for priority stocks
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const { limit = 3 } = body;

    console.log(`Starting automatic update for up to ${limit} stocks...`);

    // Get stocks to update from priority queue
    const { data: queueData, error: queueError } = await supabase
      .from('stock_update_queue')
      .select('symbol, priority_score, last_updated_at, user_requests')
      .order('priority_score', { ascending: false })
      .order('last_updated_at', { ascending: true })
      .limit(limit);

    if (queueError) {
      console.error('Error fetching update queue:', queueError);
      return NextResponse.json({ error: 'Failed to fetch update queue' }, { status: 500 });
    }

    let stocksToUpdate: string[] = [];

    if (queueData && queueData.length > 0) {
      stocksToUpdate = queueData.map(item => item.symbol);
      console.log(`Found ${stocksToUpdate.length} stocks in priority queue:`, stocksToUpdate);
    } else {
      // Fallback to major stocks that we know have good data
      const knownGoodStocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'NFLX', 'SHOP', 'PLTR'];

      // Get stocks from database that are likely to have good data (major companies)
      const { data: availableStocks } = await supabase
        .from('stocks')
        .select('symbol')
        .in('symbol', knownGoodStocks)
        .limit(limit * 2); // Get more than needed in case some fail

      if (availableStocks && availableStocks.length > 0) {
        stocksToUpdate = availableStocks.slice(0, limit).map(s => s.symbol);
      } else {
        // Ultimate fallback to known working stocks
        stocksToUpdate = knownGoodStocks.slice(0, limit);
      }

      console.log(`Queue empty, using reliable stocks:`, stocksToUpdate);
    }

    const results = [];
    const processedSymbols = [];

    // Update each stock
    for (const symbol of stocksToUpdate) {
      try {
        console.log(`Updating ${symbol}...`);

        // Get current price data
        const priceData = await polygonService.getStockPrice(symbol);

        if (priceData) {
          // Add new quote to database
          await DatabaseService.addQuote({
            symbol: symbol,
            price: priceData.price,
            change_amount: priceData.change,
            change_percent: priceData.changePercent,
            volume: priceData.volume || 0,
            market_cap: priceData.marketCap || 0,
            currency: 'USD'
          });

          results.push({
            symbol,
            success: true,
            price: priceData.price,
            change: priceData.change,
            changePercent: priceData.changePercent
          });

          processedSymbols.push(symbol);
          console.log(`Successfully updated ${symbol}: $${priceData.price.toFixed(2)}`);
        } else {
          results.push({
            symbol,
            success: false,
            error: 'No price data available'
          });

          // If a stock consistently fails, remove it from priority queue
          try {
            await supabase
              .from('stock_update_queue')
              .update({
                priority_score: 0,
                is_priority: false,
                last_updated_at: new Date().toISOString()
              })
              .eq('symbol', symbol);
            console.log(`Removed ${symbol} from priority queue due to data issues`);
          } catch (removeError) {
            console.warn(`Failed to remove ${symbol} from queue:`, removeError);
          }

          console.warn(`No price data available for ${symbol}`);
        }

        // Add delay between requests to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 12000)); // 12 second delay

      } catch (error) {
        console.error(`Error updating ${symbol}:`, error);
        results.push({
          symbol,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });

        // Remove problematic stocks from queue
        try {
          await supabase
            .from('stock_update_queue')
            .update({
              priority_score: 0,
              is_priority: false,
              last_updated_at: new Date().toISOString()
            })
            .eq('symbol', symbol);
          console.log(`Removed ${symbol} from priority queue due to errors`);
        } catch (removeError) {
          console.warn(`Failed to remove ${symbol} from queue:`, removeError);
        }
      }
    }

    // Update the queue to reflect processed stocks
    if (processedSymbols.length > 0) {
      try {
        const response = await fetch(`${request.nextUrl.origin}/api/stocks/update-queue`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ processed_symbols: processedSymbols })
        });

        if (!response.ok) {
          console.warn('Failed to update queue after processing stocks');
        }
      } catch (queueUpdateError) {
        console.warn('Error updating queue:', queueUpdateError);
      }
    }

    return NextResponse.json({
      message: `Automatic update completed for ${stocksToUpdate.length} stocks`,
      processed: processedSymbols.length,
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in POST /api/stocks/auto-update:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
