import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/supabase';
import { PolygonService } from '@/lib/polygonService';
import { supabase } from '@/lib/supabase';

const polygonService = new PolygonService();

// GET: Get next stocks to update (simplified - no more priority queue)
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');

    // Get stocks from database
    const { data: availableStocks, error } = await supabase
      .from('stocks')
      .select('symbol')
      .order('symbol', { ascending: true })
      .limit(limit);

    if (error) {
      console.error('Error fetching stocks:', error);
      return NextResponse.json({ error: 'Failed to fetch stocks' }, { status: 500 });
    }

    const symbols = availableStocks?.map(s => s.symbol) || [];

    return NextResponse.json({
      stocks_to_update: symbols,
      source: 'database_rotation',
      count: symbols.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in GET /api/stocks/auto-update:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST: Execute automatic update for priority stocks
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const { limit = 3 } = body;

    console.log(`Starting automatic update for up to ${limit} stocks...`);

    // Get all available stocks from database (no priority queue needed)
    const { data: availableStocks, error: stocksError } = await supabase
      .from('stocks')
      .select('symbol')
      .order('symbol', { ascending: true })
      .limit(limit);

    if (stocksError) {
      console.error('Error fetching stocks:', stocksError);
      return NextResponse.json({ error: 'Failed to fetch stocks' }, { status: 500 });
    }

    let stocksToUpdate: string[] = [];

    if (availableStocks && availableStocks.length > 0) {
      stocksToUpdate = availableStocks.map(s => s.symbol);
    } else {
      console.warn(`No stocks found in database for auto-update`);
      return NextResponse.json({
        message: 'No stocks available for update',
        processed: 0,
        successful: 0,
        failed: 0,
        results: [],
        timestamp: new Date().toISOString()
      });
    }

    const results = [];
    const processedSymbols = [];

    // Fast batch update using parallel processing (no rate limits with paid subscription)
    console.log(`Starting fast batch update for ${stocksToUpdate.length} stocks...`);
    const startTime = Date.now();

    // Get all prices in parallel for maximum speed
    const batchResults = await polygonService.getBatchStockPrices(stocksToUpdate);

    // Process results and update database
    const updatePromises = [];

    for (const [symbol, priceData] of Object.entries(batchResults)) {
      if (priceData) {
        // Add to results
        results.push({
          symbol,
          success: true,
          price: priceData.price,
          change: priceData.change,
          changePercent: priceData.changePercent
        });

        processedSymbols.push(symbol);

        // Prepare database update (run in parallel)
        const updatePromise = DatabaseService.addQuote({
          symbol: symbol,
          price: priceData.price,
          change_amount: priceData.change,
          change_percent: priceData.changePercent,
          volume: priceData.volume || 0,
          market_cap: 0, // Will be updated separately if needed
          currency: 'USD'
        }).then(() => {
          console.log(`Successfully updated ${symbol}: $${priceData.price.toFixed(2)}`);
        }).catch(error => {
          console.error(`Error updating ${symbol}:`, error);
        });

        updatePromises.push(updatePromise);
      } else {
        results.push({
          symbol,
          success: false,
          error: 'No price data available'
        });

        console.warn(`No price data available for ${symbol}`);
      }
    }

    // Wait for all database updates to complete
    await Promise.all(updatePromises);

    const endTime = Date.now();
    const duration = endTime - startTime;
    const successCount = results.filter(r => r.success).length;
    console.log(`Fast batch update completed in ${duration}ms: ${successCount}/${stocksToUpdate.length} successful (avg: ${Math.round(duration / stocksToUpdate.length)}ms per stock)`);

    return NextResponse.json({
      message: `Automatic update completed for ${stocksToUpdate.length} stocks in ${duration}ms`,
      processed: processedSymbols.length,
      successful: successCount,
      failed: stocksToUpdate.length - successCount,
      results,
      summary: {
        total: stocksToUpdate.length,
        successful: successCount,
        failed: stocksToUpdate.length - successCount,
        duration_ms: duration,
        avg_time_per_stock: Math.round(duration / stocksToUpdate.length)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in POST /api/stocks/auto-update:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
