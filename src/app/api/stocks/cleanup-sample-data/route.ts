import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// DELETE: Remove sample financial data from database
export async function DELETE() {
  try {
    console.log('Cleaning up sample financial data...');

    // Delete all financial data (since it was sample data)
    const { error } = await supabase
      .from('stock_financials')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

    if (error) {
      console.error('Error cleaning up financial data:', error);
      return NextResponse.json({ error: 'Failed to cleanup financial data' }, { status: 500 });
    }

    console.log('Successfully cleaned up sample financial data');

    return NextResponse.json({
      message: 'Sample financial data cleaned up successfully',
      note: 'All financial data has been removed. Ready for real API integration.',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in DELETE /api/stocks/cleanup-sample-data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET: Check what financial data exists
export async function GET() {
  try {
    const { data: financialRecords, error } = await supabase
      .from('stock_financials')
      .select('symbol, scraped_at')
      .order('scraped_at', { ascending: false });

    if (error) {
      console.error('Error fetching financial records:', error);
      return NextResponse.json({ error: 'Failed to fetch financial records' }, { status: 500 });
    }

    return NextResponse.json({
      total_records: financialRecords?.length || 0,
      records: financialRecords || [],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in GET /api/stocks/cleanup-sample-data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
