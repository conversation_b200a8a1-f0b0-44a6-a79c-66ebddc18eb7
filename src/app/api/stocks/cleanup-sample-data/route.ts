import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// DELETE: Remove all mock/sample data from database
export async function DELETE() {
  try {
    console.log('Cleaning up all mock/sample data...');

    const results = {
      financials_deleted: 0,
      quotes_deleted: 0,
      analysis_deleted: 0
    };

    // Delete all financial data (since it was sample data)
    const { error: financialsError, count: financialsCount } = await supabase
      .from('stock_financials')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

    if (financialsError) {
      console.error('Error cleaning up financial data:', financialsError);
    } else {
      results.financials_deleted = financialsCount || 0;
    }

    // Delete old quotes (keep only recent ones from last 24 hours)
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    const { error: quotesError, count: quotesCount } = await supabase
      .from('stock_quotes')
      .delete()
      .lt('scraped_at', oneDayAgo);

    if (quotesError) {
      console.error('Error cleaning up old quotes:', quotesError);
    } else {
      results.quotes_deleted = quotesCount || 0;
    }

    // Delete old analysis data
    const { error: analysisError, count: analysisCount } = await supabase
      .from('stock_analysis')
      .delete()
      .lt('calculated_at', oneDayAgo);

    if (analysisError) {
      console.error('Error cleaning up old analysis:', analysisError);
    } else {
      results.analysis_deleted = analysisCount || 0;
    }

    console.log('Successfully cleaned up mock/sample data:', results);

    return NextResponse.json({
      message: 'Mock/sample data cleaned up successfully',
      results,
      note: 'Database is now ready for real data only.',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in DELETE /api/stocks/cleanup-sample-data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET: Check what financial data exists
export async function GET() {
  try {
    const { data: financialRecords, error } = await supabase
      .from('stock_financials')
      .select('symbol, scraped_at')
      .order('scraped_at', { ascending: false });

    if (error) {
      console.error('Error fetching financial records:', error);
      return NextResponse.json({ error: 'Failed to fetch financial records' }, { status: 500 });
    }

    return NextResponse.json({
      total_records: financialRecords?.length || 0,
      records: financialRecords || [],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in GET /api/stocks/cleanup-sample-data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
