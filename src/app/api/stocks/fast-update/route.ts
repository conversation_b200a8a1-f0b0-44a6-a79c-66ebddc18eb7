import { NextRequest, NextResponse } from 'next/server';
import { polygonService } from '@/lib/polygonService';
import { DatabaseService, supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const { symbols, limit = 20 } = body;

    let stocksToUpdate: string[] = [];

    if (symbols && Array.isArray(symbols) && symbols.length > 0) {
      // Use provided symbols
      stocksToUpdate = symbols.slice(0, limit);
    } else {
      // Get stocks from database
      try {
        const { data: availableStocks, error } = await supabase
          .from('stocks')
          .select('symbol')
          .order('symbol', { ascending: true })
          .limit(limit);

        if (error) {
          console.error('Error fetching stocks:', error);
          return NextResponse.json(
            { error: 'Failed to fetch stocks from database' },
            { status: 500 }
          );
        }

        if (availableStocks && availableStocks.length > 0) {
          stocksToUpdate = availableStocks.map(s => s.symbol);
        } else {
          return NextResponse.json({
            message: 'No stocks available for update',
            results: [],
            summary: { total: 0, successful: 0, failed: 0, duration_ms: 0, avg_time_per_stock: 0 },
            timestamp: new Date().toISOString()
          });
        }
      } catch (dbError) {
        console.error('Could not fetch stocks from database:', dbError);
        return NextResponse.json(
          { error: 'Failed to fetch stocks from database', details: dbError instanceof Error ? dbError.message : 'Unknown error' },
          { status: 500 }
        );
      }
    }

    console.log(`Fast updating prices for ${stocksToUpdate.length} stocks...`);
    const startTime = Date.now();

    // Get all prices in parallel for maximum speed
    const batchResults = await polygonService.getBatchStockPrices(stocksToUpdate);
    
    const results = [];
    const updatePromises = [];

    // Process results and prepare database updates
    for (const [symbol, priceData] of Object.entries(batchResults)) {
      if (priceData) {
        // Add to results
        results.push({
          symbol,
          success: true,
          price: priceData.price,
          change: priceData.change,
          changePercent: priceData.changePercent,
          volume: priceData.volume || 0
        });

        // Prepare database update (run in parallel)
        const updatePromise = DatabaseService.addQuote({
          symbol: symbol,
          price: priceData.price,
          change_amount: priceData.change,
          change_percent: priceData.changePercent,
          volume: priceData.volume || 0,
          market_cap: priceData.marketCap || 0,
          currency: 'USD',
          week_52_low: 0,
          week_52_high: 0
        }).catch(error => {
          console.error(`Error updating quote for ${symbol}:`, error);
          return null;
        });

        updatePromises.push(updatePromise);
      } else {
        results.push({
          symbol,
          success: false,
          error: 'No price data available'
        });
      }
    }

    // Wait for all database updates to complete
    await Promise.all(updatePromises);

    const endTime = Date.now();
    const duration = endTime - startTime;
    const successCount = results.filter(r => r.success).length;

    console.log(`Fast update completed: ${successCount}/${stocksToUpdate.length} successful in ${duration}ms`);

    return NextResponse.json({
      message: `Fast updated ${stocksToUpdate.length} stocks in ${duration}ms`,
      results,
      summary: {
        total: stocksToUpdate.length,
        successful: successCount,
        failed: stocksToUpdate.length - successCount,
        duration_ms: duration,
        avg_time_per_stock: Math.round(duration / stocksToUpdate.length)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in fast update API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Handle CORS for development
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
