import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '8');

    // Get popular stocks based on recent quote activity (most frequently updated)
    const { data: popularStocks, error } = await supabase
      .from('stock_quotes')
      .select('symbol, COUNT(*) as update_count')
      .gte('scraped_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
      .group('symbol')
      .order('update_count', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching popular stocks:', error);
      
      // Fallback to stocks that exist in the database
      const { data: fallbackStocks } = await supabase
        .from('stocks')
        .select('symbol')
        .order('created_at', { ascending: false })
        .limit(limit);

      const symbols = fallbackStocks?.map(s => s.symbol) || [];
      
      return NextResponse.json({
        symbols,
        source: 'fallback',
        count: symbols.length,
        timestamp: new Date().toISOString()
      });
    }

    const symbols = popularStocks?.map(s => s.symbol) || [];

    return NextResponse.json({
      symbols,
      source: 'activity_based',
      count: symbols.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in popular stocks API:', error);
    
    // Ultimate fallback - return empty array
    return NextResponse.json({
      symbols: [],
      source: 'error_fallback',
      count: 0,
      error: 'Failed to fetch popular stocks',
      timestamp: new Date().toISOString()
    });
  }
}
