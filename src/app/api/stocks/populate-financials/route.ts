import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// POST: Populate financial data for stocks that don't have it
export async function POST(request: NextRequest) {
  try {
    console.log('Populating financial data for stocks...');

    // Get stocks that don't have financial data
    const { data: stocksWithoutFinancials, error: stocksError } = await supabase
      .from('stocks')
      .select('symbol, name')
      .not('symbol', 'in', `(
        SELECT DISTINCT symbol 
        FROM stock_financials 
        WHERE scraped_at > NOW() - INTERVAL '30 days'
      )`);

    if (stocksError) {
      console.error('Error fetching stocks without financials:', stocksError);
      return NextResponse.json({ error: 'Failed to fetch stocks' }, { status: 500 });
    }

    if (!stocksWithoutFinancials || stocksWithoutFinancials.length === 0) {
      return NextResponse.json({
        message: 'All stocks already have recent financial data',
        processed: 0,
        timestamp: new Date().toISOString()
      });
    }

    // Note: This endpoint is prepared for real financial data integration
    // Currently returns message that real data integration is needed

    return NextResponse.json({
      message: 'Financial data population requires real API integration. Sample data removed as requested.',
      note: 'To populate financial data, integrate with a real financial data API like Alpha Vantage, Yahoo Finance, or Polygon.io',
      stocks_without_financials: stocksWithoutFinancials.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in POST /api/stocks/populate-financials:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET: Check financial data coverage
export async function GET() {
  try {
    const { data: totalStocks } = await supabase
      .from('stocks')
      .select('symbol', { count: 'exact' });

    const { data: stocksWithFinancials } = await supabase
      .from('stock_financials')
      .select('symbol', { count: 'exact' })
      .gte('scraped_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()); // Last 30 days

    const coverage = {
      total_stocks: totalStocks?.length || 0,
      stocks_with_financials: stocksWithFinancials?.length || 0,
      coverage_percentage: totalStocks?.length ? 
        Math.round((stocksWithFinancials?.length || 0) / totalStocks.length * 100) : 0
    };

    return NextResponse.json({
      coverage,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in GET /api/stocks/populate-financials:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
