import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// POST: Populate the update queue with major stocks that have good data
export async function POST(request: NextRequest) {
  try {
    console.log('Populating update queue with major stocks...');

    // List of major stocks that typically have good market cap and shares data
    const majorStocks = [
      'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'NFLX', 
      'SHOP', 'PLTR', 'AMD', 'INTC', 'CRM', 'ORCL', 'ADBE', 'PYPL',
      'DIS', 'UBER', 'LYFT', 'COIN', 'SQ', 'ROKU', 'ZM', 'SNOW'
    ];

    const results = [];
    let added = 0;
    let skipped = 0;

    for (const symbol of majorStocks) {
      try {
        // Check if stock exists in our database
        const { data: stockExists } = await supabase
          .from('stocks')
          .select('symbol')
          .eq('symbol', symbol)
          .single();

        if (!stockExists) {
          console.log(`Stock ${symbol} not in database, skipping...`);
          skipped++;
          continue;
        }

        // Check if already in queue
        const { data: existingQueue } = await supabase
          .from('stock_update_queue')
          .select('symbol')
          .eq('symbol', symbol)
          .single();

        if (existingQueue) {
          console.log(`${symbol} already in queue, skipping...`);
          skipped++;
          continue;
        }

        // Add to queue with base priority
        const { data, error } = await supabase
          .from('stock_update_queue')
          .insert({
            symbol: symbol,
            priority_score: 5, // Base priority
            user_requests: 0,
            last_requested_at: null,
            last_updated_at: null,
            is_priority: false
          })
          .select()
          .single();

        if (error) {
          console.error(`Error adding ${symbol} to queue:`, error);
          results.push({ symbol, success: false, error: error.message });
        } else {
          console.log(`Added ${symbol} to update queue`);
          results.push({ symbol, success: true });
          added++;
        }

      } catch (error) {
        console.error(`Error processing ${symbol}:`, error);
        results.push({ 
          symbol, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    return NextResponse.json({
      message: `Queue population completed: ${added} added, ${skipped} skipped`,
      added,
      skipped,
      total_processed: majorStocks.length,
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in POST /api/stocks/populate-queue:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET: Check current queue status
export async function GET(request: NextRequest) {
  try {
    const { data: queueData, error } = await supabase
      .from('stock_update_queue')
      .select('symbol, priority_score, user_requests, last_updated_at, is_priority')
      .order('priority_score', { ascending: false })
      .order('last_updated_at', { ascending: true });

    if (error) {
      console.error('Error fetching queue:', error);
      return NextResponse.json({ error: 'Failed to fetch queue' }, { status: 500 });
    }

    const stats = {
      total_stocks: queueData?.length || 0,
      priority_stocks: queueData?.filter(s => s.is_priority).length || 0,
      never_updated: queueData?.filter(s => !s.last_updated_at).length || 0,
      recently_updated: queueData?.filter(s => {
        if (!s.last_updated_at) return false;
        const hoursSince = (new Date().getTime() - new Date(s.last_updated_at).getTime()) / (1000 * 60 * 60);
        return hoursSince < 1;
      }).length || 0
    };

    return NextResponse.json({
      queue: queueData || [],
      stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in GET /api/stocks/populate-queue:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
