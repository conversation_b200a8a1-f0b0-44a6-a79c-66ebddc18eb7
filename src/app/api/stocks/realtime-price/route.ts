import { NextRequest, NextResponse } from 'next/server';
import { polygonService } from '@/lib/polygonService';
import { DatabaseService } from '@/lib/supabase';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const { symbol } = await request.json();

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol is required' },
        { status: 400 }
      );
    }

    const upperSymbol = symbol.toUpperCase();
    console.log(`Getting real-time price for ${upperSymbol}...`);

    const startTime = Date.now();

    // Get real-time price data
    const priceData = await polygonService.getStockPrice(upperSymbol);

    if (!priceData) {
      return NextResponse.json(
        { error: `No price data available for ${upperSymbol}` },
        { status: 404 }
      );
    }

    // Update the database with fresh data
    try {
      await DatabaseService.addQuote({
        symbol: upperSymbol,
        price: priceData.price,
        change_amount: priceData.change,
        change_percent: priceData.changePercent,
        volume: priceData.volume || 0,
        market_cap: priceData.marketCap || 0,
        currency: 'USD',
        week_52_low: 0,
        week_52_high: 0
      });

      console.log(`Updated real-time price for ${upperSymbol}: $${priceData.price.toFixed(2)}`);
    } catch (dbError) {
      console.warn(`Failed to update database for ${upperSymbol}:`, dbError);
      // Continue even if database update fails
    }

    // Log user interaction for analytics (optional)
    console.log(`User requested real-time price for ${upperSymbol}`);

    const duration = Date.now() - startTime;

    return NextResponse.json({
      symbol: upperSymbol,
      price: priceData.price,
      change: priceData.change,
      changePercent: priceData.changePercent,
      volume: priceData.volume || 0,
      marketCap: priceData.marketCap || 0,
      timestamp: new Date().toISOString(),
      responseTime: duration,
      source: 'real-time'
    });

  } catch (error) {
    console.error('Error in real-time price API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch real-time price', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// GET: Get cached price data for a symbol
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol');

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol parameter is required' },
        { status: 400 }
      );
    }

    const upperSymbol = symbol.toUpperCase();

    // Get latest quote from database
    const { data: quote, error } = await supabase
      .from('stock_quotes')
      .select('*')
      .eq('symbol', upperSymbol)
      .order('scraped_at', { ascending: false })
      .limit(1)
      .single();

    if (error || !quote) {
      return NextResponse.json(
        { error: `No cached price data found for ${upperSymbol}` },
        { status: 404 }
      );
    }

    return NextResponse.json({
      symbol: upperSymbol,
      price: quote.price,
      change: quote.change_amount,
      changePercent: quote.change_percent,
      volume: quote.volume,
      marketCap: quote.market_cap,
      timestamp: quote.scraped_at,
      source: 'cached'
    });

  } catch (error) {
    console.error('Error in cached price API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cached price' },
      { status: 500 }
    );
  }
}

// Handle CORS for development
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
