import { NextRequest, NextResponse } from 'next/server';
import { polygonService } from '@/lib/polygonService';
import { DatabaseService } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const { symbols } = body;

    // If no symbols provided, default to major stocks for testing
    const stocksToUpdate = symbols && Array.isArray(symbols) ? symbols : ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA'];

    console.log(`Refreshing prices for ${stocksToUpdate.length} stocks...`);
    const results = [];

    for (const symbol of stocksToUpdate) {
      try {
        console.log(`Getting price data for ${symbol}...`);
        const priceData = await polygonService.getStockPrice(symbol);
        
        if (priceData) {
          // Update the quote in the database
          await DatabaseService.addQuote({
            symbol: symbol,
            price: priceData.price,
            change_amount: priceData.change,
            change_percent: priceData.changePercent,
            volume: 0,
            market_cap: priceData.marketCap || 0,
            currency: 'USD',
            week_52_low: 0,
            week_52_high: 0
          });

          results.push({
            symbol,
            success: true,
            price: priceData.price,
            change: priceData.change,
            changePercent: priceData.changePercent
          });

          console.log(`Updated ${symbol}: $${priceData.price.toFixed(2)} (${priceData.changePercent.toFixed(2)}%)`);
        } else {
          results.push({
            symbol,
            success: false,
            error: 'No price data available'
          });
          console.log(`No price data available for ${symbol}`);
        }
      } catch (error) {
        console.error(`Error updating ${symbol}:`, error);
        results.push({
          symbol,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return NextResponse.json({
      message: `Processed ${stocksToUpdate.length} stocks`,
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in refresh prices API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle CORS for development
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
