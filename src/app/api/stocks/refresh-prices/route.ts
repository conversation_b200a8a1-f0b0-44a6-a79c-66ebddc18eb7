import { NextRequest, NextResponse } from 'next/server';
import { polygonService } from '@/lib/polygonService';
import { DatabaseService } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const { symbols } = body;

    // If no symbols provided, return error - no more hardcoded defaults
    if (!symbols || !Array.isArray(symbols) || symbols.length === 0) {
      return NextResponse.json(
        { error: 'Symbols array is required and must not be empty' },
        { status: 400 }
      );
    }

    const stocksToUpdate = symbols;

    console.log(`Fast refreshing prices for ${stocksToUpdate.length} stocks...`);
    const startTime = Date.now();

    // Get all prices in parallel for maximum speed
    const batchResults = await polygonService.getBatchStockPrices(stocksToUpdate);

    const results = [];
    const updatePromises = [];

    // Process results and prepare database updates
    for (const [symbol, priceData] of Object.entries(batchResults)) {
      if (priceData) {
        // Add to results
        results.push({
          symbol,
          success: true,
          price: priceData.price,
          change: priceData.change,
          changePercent: priceData.changePercent
        });

        // Prepare database update (run in parallel)
        const updatePromise = DatabaseService.addQuote({
          symbol: symbol,
          price: priceData.price,
          change_amount: priceData.change,
          change_percent: priceData.changePercent,
          volume: priceData.volume || 0,
          market_cap: 0, // Will be updated separately if needed
          currency: 'USD',
          week_52_low: 0,
          week_52_high: 0
        }).then(() => {
          console.log(`Updated ${symbol}: $${priceData.price.toFixed(2)} (${priceData.changePercent.toFixed(2)}%)`);
        }).catch(error => {
          console.error(`Error updating ${symbol}:`, error);
        });

        updatePromises.push(updatePromise);
      } else {
        results.push({
          symbol,
          success: false,
          error: 'No price data available'
        });
        console.log(`No price data available for ${symbol}`);
      }
    }

    // Wait for all database updates to complete
    await Promise.all(updatePromises);

    const endTime = Date.now();
    const duration = endTime - startTime;
    const successCount = results.filter(r => r.success).length;

    console.log(`Fast refresh completed: ${successCount}/${stocksToUpdate.length} successful in ${duration}ms`);

    return NextResponse.json({
      message: `Fast processed ${stocksToUpdate.length} stocks in ${duration}ms`,
      results,
      summary: {
        total: stocksToUpdate.length,
        successful: successCount,
        failed: stocksToUpdate.length - successCount,
        duration_ms: duration,
        avg_time_per_stock: Math.round(duration / stocksToUpdate.length)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in refresh prices API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle CORS for development
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
