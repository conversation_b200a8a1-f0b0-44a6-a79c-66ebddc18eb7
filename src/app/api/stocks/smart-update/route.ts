import { NextRequest, NextResponse } from 'next/server';
import { polygonService } from '@/lib/polygonService';
import { DatabaseService } from '@/lib/supabase';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const { limit = 50, includeFinancials = false } = await request.json();

    console.log(`Starting smart update for up to ${limit} stocks...`);
    const startTime = Date.now();

    // Get stocks prioritized by multiple factors
    const stocksToUpdate = await getSmartUpdateList(limit);

    if (stocksToUpdate.length === 0) {
      return NextResponse.json({
        message: 'No stocks available for smart update',
        results: [],
        summary: { total: 0, successful: 0, failed: 0, duration_ms: 0 },
        timestamp: new Date().toISOString()
      });
    }

    console.log(`Smart update selected ${stocksToUpdate.length} stocks based on priority factors`);

    // Get all prices in parallel for maximum speed
    const batchResults = await polygonService.getBatchStockPrices(stocksToUpdate);
    
    const results = [];
    const updatePromises = [];

    // Process results and update database
    for (const [symbol, priceData] of Object.entries(batchResults)) {
      if (priceData) {
        results.push({
          symbol,
          success: true,
          price: priceData.price,
          change: priceData.change,
          changePercent: priceData.changePercent,
          volume: priceData.volume || 0
        });

        // Prepare database update (run in parallel)
        const updatePromise = DatabaseService.addQuote({
          symbol: symbol,
          price: priceData.price,
          change_amount: priceData.change,
          change_percent: priceData.changePercent,
          volume: priceData.volume || 0,
          market_cap: priceData.marketCap || 0,
          currency: 'USD',
          week_52_low: 0,
          week_52_high: 0
        }).then(() => {
          console.log(`Smart updated ${symbol}: $${priceData.price.toFixed(2)}`);
        }).catch(error => {
          console.error(`Error updating ${symbol}:`, error);
        });

        updatePromises.push(updatePromise);

        // Also update financial metrics if requested
        if (includeFinancials) {
          const financialPromise = polygonService.getFinancialMetrics(symbol)
            .then(metrics => {
              if (metrics) {
                return DatabaseService.updateFinancials(symbol, {
                  symbol,
                  market_cap: metrics.marketCap || 0,
                  pe_ratio: metrics.peRatio || 0,
                  return_on_equity: metrics.returnOnEquity || 0,
                  debt_to_equity: metrics.debtToEquity || 0,
                  profit_margins: metrics.profitMargin || 0,
                  earnings_per_share: metrics.earningsPerShare || 0,
                  scraped_at: new Date().toISOString()
                });
              }
            })
            .catch(error => {
              console.warn(`Failed to update financials for ${symbol}:`, error);
            });

          updatePromises.push(financialPromise);
        }
      } else {
        results.push({
          symbol,
          success: false,
          error: 'No price data available'
        });
      }
    }

    // Wait for all database updates to complete
    await Promise.all(updatePromises);

    const endTime = Date.now();
    const duration = endTime - startTime;
    const successCount = results.filter(r => r.success).length;

    console.log(`Smart update completed in ${duration}ms: ${successCount}/${stocksToUpdate.length} successful`);

    return NextResponse.json({
      message: `Smart update completed for ${stocksToUpdate.length} stocks in ${duration}ms`,
      results,
      summary: {
        total: stocksToUpdate.length,
        successful: successCount,
        failed: stocksToUpdate.length - successCount,
        duration_ms: duration,
        avg_time_per_stock: Math.round(duration / stocksToUpdate.length),
        included_financials: includeFinancials
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in smart update API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Smart algorithm to select stocks for update based on multiple factors
async function getSmartUpdateList(limit: number): Promise<string[]> {
  try {
    // Get stocks with their last update times and activity
    const { data: stocksWithActivity, error } = await supabase
      .from('stocks')
      .select(`
        symbol,
        created_at,
        stock_quotes!inner(scraped_at),
        stock_analysis(calculated_at)
      `)
      .order('symbol', { ascending: true });

    if (error || !stocksWithActivity) {
      console.error('Error fetching stocks for smart update:', error);
      return [];
    }

    // Calculate priority scores for each stock
    const now = new Date();
    const stockPriorities = stocksWithActivity.map(stock => {
      let score = 0;

      // Factor 1: Time since last price update (higher score for older data)
      const lastQuoteTime = stock.stock_quotes?.[0]?.scraped_at;
      if (lastQuoteTime) {
        const minutesSinceUpdate = (now.getTime() - new Date(lastQuoteTime).getTime()) / (1000 * 60);
        score += Math.min(minutesSinceUpdate / 60, 10); // Max 10 points for 1+ hour old data
      } else {
        score += 20; // High priority for stocks with no price data
      }

      // Factor 2: Missing analysis data
      if (!stock.stock_analysis || stock.stock_analysis.length === 0) {
        score += 15; // High priority for stocks without analysis
      }

      // Factor 3: Randomization to ensure variety
      score += Math.random() * 5;

      return {
        symbol: stock.symbol,
        score
      };
    });

    // Sort by priority score (highest first) and take the top stocks
    const selectedStocks = stockPriorities
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(item => item.symbol);

    console.log(`Smart update algorithm selected stocks based on data freshness and completeness`);
    return selectedStocks;

  } catch (error) {
    console.error('Error in smart update selection:', error);
    return [];
  }
}

// GET: Get the current smart update priority list
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');

    const smartList = await getSmartUpdateList(limit);

    return NextResponse.json({
      stocks: smartList,
      count: smartList.length,
      algorithm: 'data_freshness_and_completeness',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in smart update GET API:', error);
    return NextResponse.json(
      { error: 'Failed to get smart update list' },
      { status: 500 }
    );
  }
}

// Handle CORS for development
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
