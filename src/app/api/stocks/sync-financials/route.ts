import { NextRequest, NextResponse } from 'next/server';
import { polygonService } from '@/lib/polygonService';
import { DatabaseService } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const { symbol, symbols } = await request.json();

    let stocksToProcess: string[] = [];

    if (symbol) {
      stocksToProcess = [symbol.toUpperCase()];
    } else if (symbols && Array.isArray(symbols)) {
      stocksToProcess = symbols.map(s => s.toUpperCase());
    } else {
      return NextResponse.json(
        { error: 'Either symbol or symbols array is required' },
        { status: 400 }
      );
    }

    console.log(`Syncing financial metrics for ${stocksToProcess.length} stocks...`);
    const startTime = Date.now();

    const results = [];
    const updatePromises = [];

    for (const stockSymbol of stocksToProcess) {
      try {
        console.log(`Getting financial metrics for ${stockSymbol}...`);
        
        // Get comprehensive financial metrics
        const metrics = await polygonService.getFinancialMetrics(stockSymbol);
        
        if (metrics) {
          // Prepare database update
          const financialData = {
            symbol: stockSymbol,
            market_cap: metrics.marketCap || 0,
            pe_ratio: metrics.peRatio || 0,
            return_on_equity: metrics.returnOnEquity || 0,
            debt_to_equity: metrics.debtToEquity || 0,
            profit_margins: metrics.profitMargin || 0,
            current_ratio: metrics.currentRatio || 0,
            earnings_per_share: metrics.earningsPerShare || 0,
            revenue: metrics.revenue || 0,
            net_income: metrics.netIncome || 0,
            total_assets: metrics.totalAssets || 0,
            total_debt: metrics.totalDebt || 0,
            shareholders_equity: metrics.shareholders_equity || 0,
            shares_outstanding: metrics.sharesOutstanding || 0,
            scraped_at: new Date().toISOString()
          };

          // Update database (run in parallel)
          const updatePromise = DatabaseService.updateFinancials(stockSymbol, financialData)
            .then(() => {
              console.log(`Updated financial metrics for ${stockSymbol}`);
              return { symbol: stockSymbol, success: true, metrics };
            })
            .catch(error => {
              console.error(`Error updating financials for ${stockSymbol}:`, error);
              return { symbol: stockSymbol, success: false, error: error.message };
            });

          updatePromises.push(updatePromise);

          results.push({
            symbol: stockSymbol,
            success: true,
            metrics: {
              peRatio: metrics.peRatio,
              marketCap: metrics.marketCap,
              returnOnEquity: metrics.returnOnEquity,
              debtToEquity: metrics.debtToEquity,
              profitMargin: metrics.profitMargin,
              earningsPerShare: metrics.earningsPerShare
            }
          });
        } else {
          results.push({
            symbol: stockSymbol,
            success: false,
            error: 'No financial metrics available'
          });
          console.warn(`No financial metrics available for ${stockSymbol}`);
        }
      } catch (error) {
        console.error(`Error processing ${stockSymbol}:`, error);
        results.push({
          symbol: stockSymbol,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Wait for all database updates to complete
    const updateResults = await Promise.all(updatePromises);

    const endTime = Date.now();
    const duration = endTime - startTime;
    const successCount = results.filter(r => r.success).length;

    console.log(`Financial metrics sync completed in ${duration}ms: ${successCount}/${stocksToProcess.length} successful`);

    return NextResponse.json({
      message: `Financial metrics sync completed for ${stocksToProcess.length} stocks in ${duration}ms`,
      results,
      updateResults,
      summary: {
        total: stocksToProcess.length,
        successful: successCount,
        failed: stocksToProcess.length - successCount,
        duration_ms: duration,
        avg_time_per_stock: Math.round(duration / stocksToProcess.length)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in financial metrics sync API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// GET: Get financial metrics for a single stock
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol');

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol parameter is required' },
        { status: 400 }
      );
    }

    const upperSymbol = symbol.toUpperCase();
    console.log(`Getting financial metrics for ${upperSymbol}...`);

    const metrics = await polygonService.getFinancialMetrics(upperSymbol);

    if (!metrics) {
      return NextResponse.json(
        { error: `No financial metrics available for ${upperSymbol}` },
        { status: 404 }
      );
    }

    return NextResponse.json({
      symbol: upperSymbol,
      metrics,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in financial metrics GET API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch financial metrics' },
      { status: 500 }
    );
  }
}

// Handle CORS for development
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
