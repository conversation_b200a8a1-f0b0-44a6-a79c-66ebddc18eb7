import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/supabase';
import { supabase } from '@/lib/supabase';

// Calculate priority score based on various factors
function calculatePriorityScore(
  userRequests: number,
  lastUpdatedAt: Date | null,
  lastRequestedAt: Date | null
): number {
  let score = 0;
  const now = new Date();

  // Base score from user requests (more requests = higher priority)
  score += userRequests * 10;

  // Time since last update (older = higher priority)
  if (lastUpdatedAt) {
    const hoursSinceUpdate = (now.getTime() - lastUpdatedAt.getTime()) / (1000 * 60 * 60);
    if (hoursSinceUpdate > 24) score += 50; // Very old
    else if (hoursSinceUpdate > 12) score += 30; // Old
    else if (hoursSinceUpdate > 6) score += 20; // Somewhat old
    else if (hoursSinceUpdate > 1) score += 10; // Recent
    else score -= 20; // Very recent (malus)
  } else {
    score += 100; // Never updated = highest priority
  }

  // Time since last request (recent requests get bonus)
  if (lastRequestedAt) {
    const hoursSinceRequest = (now.getTime() - lastRequestedAt.getTime()) / (1000 * 60 * 60);
    if (hoursSinceRequest < 1) score += 15; // Very recent request
    else if (hoursSinceRequest < 6) score += 10; // Recent request
  }

  return Math.max(0, score);
}

// GET: Get current update queue status
export async function GET(request: NextRequest) {
  try {
    const { data: queue, error } = await supabase
      .from('stock_update_queue')
      .select('*')
      .order('priority_score', { ascending: false })
      .order('last_updated_at', { ascending: true })
      .limit(20);

    if (error) {
      console.error('Error fetching update queue:', error);
      return NextResponse.json({ error: 'Failed to fetch update queue' }, { status: 500 });
    }

    // Update queue positions
    const updatedQueue = queue?.map((item, index) => ({
      ...item,
      queue_position: index + 1
    })) || [];

    return NextResponse.json({
      queue: updatedQueue,
      total: queue?.length || 0,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in GET /api/stocks/update-queue:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST: Request priority update for a stock
export async function POST(request: NextRequest) {
  try {
    const { symbol } = await request.json();

    if (!symbol) {
      return NextResponse.json({ error: 'Symbol is required' }, { status: 400 });
    }

    const upperSymbol = symbol.toUpperCase();

    // Check if stock exists in our database
    const { data: stock } = await supabase
      .from('stocks')
      .select('symbol')
      .eq('symbol', upperSymbol)
      .single();

    if (!stock) {
      return NextResponse.json({ error: 'Stock not found in database' }, { status: 404 });
    }

    // Get current queue entry or create new one
    const { data: existingEntry } = await supabase
      .from('stock_update_queue')
      .select('*')
      .eq('symbol', upperSymbol)
      .single();

    const now = new Date();
    let newUserRequests = 1;
    let lastUpdatedAt = null;

    if (existingEntry) {
      newUserRequests = (existingEntry.user_requests || 0) + 1;
      lastUpdatedAt = existingEntry.last_updated_at;
    }

    // Get last update time from stock_quotes
    const { data: lastQuote } = await supabase
      .from('stock_quotes')
      .select('scraped_at')
      .eq('symbol', upperSymbol)
      .order('scraped_at', { ascending: false })
      .limit(1)
      .single();

    if (lastQuote?.scraped_at) {
      lastUpdatedAt = new Date(lastQuote.scraped_at);
    }

    // Calculate new priority score
    const priorityScore = calculatePriorityScore(
      newUserRequests,
      lastUpdatedAt,
      now
    );

    // Upsert the queue entry
    const { data: updatedEntry, error } = await supabase
      .from('stock_update_queue')
      .upsert({
        symbol: upperSymbol,
        priority_score: priorityScore,
        user_requests: newUserRequests,
        last_requested_at: now.toISOString(),
        last_updated_at: lastUpdatedAt?.toISOString(),
        is_priority: true,
        updated_at: now.toISOString()
      }, {
        onConflict: 'symbol'
      })
      .select()
      .single();

    if (error) {
      console.error('Error updating queue:', error);
      return NextResponse.json({ error: 'Failed to update queue' }, { status: 500 });
    }

    // Get updated queue position
    const { data: queueData } = await supabase
      .from('stock_update_queue')
      .select('symbol, priority_score')
      .order('priority_score', { ascending: false })
      .order('last_updated_at', { ascending: true });

    const queuePosition = queueData?.findIndex(item => item.symbol === upperSymbol) + 1 || 0;

    return NextResponse.json({
      message: `Priority update requested for ${upperSymbol}`,
      symbol: upperSymbol,
      priority_score: priorityScore,
      user_requests: newUserRequests,
      queue_position: queuePosition,
      last_updated_at: lastUpdatedAt?.toISOString(),
      timestamp: now.toISOString()
    });

  } catch (error) {
    console.error('Error in POST /api/stocks/update-queue:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT: Update queue after processing stocks (called by update service)
export async function PUT(request: NextRequest) {
  try {
    const { processed_symbols } = await request.json();

    if (!processed_symbols || !Array.isArray(processed_symbols)) {
      return NextResponse.json({ error: 'processed_symbols array is required' }, { status: 400 });
    }

    const now = new Date();
    const results = [];

    for (const symbol of processed_symbols) {
      // Update the queue entry to reflect that it was processed
      const { data, error } = await supabase
        .from('stock_update_queue')
        .update({
          last_updated_at: now.toISOString(),
          is_priority: false,
          user_requests: 0, // Reset user requests after update
          priority_score: 0, // Reset priority score to move to back of queue
          updated_at: now.toISOString()
        })
        .eq('symbol', symbol.toUpperCase())
        .select()
        .single();

      if (error) {
        console.error(`Error updating queue for ${symbol}:`, error);
        results.push({ symbol, success: false, error: error.message });
      } else {
        console.log(`Reset priority for ${symbol} - moved to back of queue`);
        results.push({ symbol, success: true, updated_at: now.toISOString() });
      }
    }

    // Also populate queue with more stocks if it's getting empty
    const { data: queueCount } = await supabase
      .from('stock_update_queue')
      .select('id', { count: 'exact' });

    if ((queueCount?.length || 0) < 5) {
      console.log('Queue getting empty, adding more stocks...');

      // Add some major stocks to the queue if they're not already there
      const majorStocks = ['MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'NFLX', 'SHOP'];

      for (const stock of majorStocks) {
        try {
          const { data: existing } = await supabase
            .from('stock_update_queue')
            .select('symbol')
            .eq('symbol', stock)
            .single();

          if (!existing) {
            // Add to queue with low priority
            await supabase
              .from('stock_update_queue')
              .insert({
                symbol: stock,
                priority_score: 1,
                user_requests: 0,
                last_requested_at: null,
                last_updated_at: null,
                is_priority: false
              });
            console.log(`Added ${stock} to update queue`);
          }
        } catch (addError) {
          console.warn(`Failed to add ${stock} to queue:`, addError);
        }
      }
    }

    return NextResponse.json({
      message: `Updated queue for ${processed_symbols.length} stocks`,
      results,
      timestamp: now.toISOString()
    });

  } catch (error) {
    console.error('Error in PUT /api/stocks/update-queue:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
