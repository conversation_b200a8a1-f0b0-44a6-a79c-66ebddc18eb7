import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Value Stock Invest - AI-Powered Stock Analysis",
  description: "Get the best stock recommendations with real-time analysis using advanced financial metrics and AI-powered scoring.",
  keywords: "stock analysis, investment, financial metrics, stock recommendations, value investing",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased bg-gray-50`}>
        <div className="min-h-screen">
          {children}
        </div>
      </body>
    </html>
  );
}
