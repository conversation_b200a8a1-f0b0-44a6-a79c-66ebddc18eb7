'use client';

import { useEffect, useRef, useState } from 'react';

interface AutoUpdateServiceProps {
  enabled?: boolean;
  intervalMinutes?: number;
  batchSize?: number;
  onUpdateComplete?: (results: any) => void;
}

export default function AutoUpdateService({
  enabled = true,
  intervalMinutes = 1, // Faster updates with no rate limits
  batchSize = 150, // Update more stocks per batch
  onUpdateComplete
}: AutoUpdateServiceProps) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isRunningRef = useRef(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [updateCount, setUpdateCount] = useState(0);

  const runAutoUpdate = async () => {
    // Prevent multiple simultaneous updates
    if (isRunningRef.current) {
      console.log('Auto-update already running, skipping...');
      return;
    }

    isRunningRef.current = true;

    try {
      console.log(`Starting fast automatic stock update (batch size: ${batchSize})...`);
      const startTime = Date.now();

      // Use the smart-update endpoint for intelligent stock selection
      const response = await fetch('/api/stocks/smart-update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          limit: batchSize,
          includeFinancials: updateCount % 5 === 0 // Update financials every 5th cycle
        })
      });

      if (response.ok) {
        const data = await response.json();
        const duration = Date.now() - startTime;

        console.log(`Smart auto-update completed in ${duration}ms:`, data.summary);
        setLastUpdate(new Date());
        setUpdateCount(prev => prev + 1);

        // Notify parent component if callback provided
        if (onUpdateComplete) {
          onUpdateComplete(data);
        }
      } else {
        console.error('Auto-update failed:', response.statusText);
      }
    } catch (error) {
      console.error('Error during auto-update:', error);
    } finally {
      isRunningRef.current = false;
    }
  };

  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Run initial update after 10 seconds
    const initialTimeout = setTimeout(() => {
      runAutoUpdate();
    }, 10000);

    // Set up recurring updates
    intervalRef.current = setInterval(() => {
      runAutoUpdate();
    }, intervalMinutes * 60 * 1000);

    console.log(`Auto-update service started: updating every ${intervalMinutes} minute(s)`);

    return () => {
      clearTimeout(initialTimeout);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      console.log('Auto-update service stopped');
    };
  }, [enabled, intervalMinutes]);

  // This component doesn't render anything
  return null;
}
