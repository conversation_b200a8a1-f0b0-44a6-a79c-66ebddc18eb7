'use client';

import { useEffect, useRef } from 'react';

interface AutoUpdateServiceProps {
  enabled?: boolean;
  intervalMinutes?: number;
}

export default function AutoUpdateService({ 
  enabled = true, 
  intervalMinutes = 1 
}: AutoUpdateServiceProps) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isRunningRef = useRef(false);

  const runAutoUpdate = async () => {
    // Prevent multiple simultaneous updates
    if (isRunningRef.current) {
      console.log('Auto-update already running, skipping...');
      return;
    }

    isRunningRef.current = true;
    
    try {
      console.log('Starting automatic stock update...');
      
      const response = await fetch('/api/stocks/auto-update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ limit: 3 })
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`Auto-update completed: ${data.processed} stocks updated`, data.results);
      } else {
        console.error('Auto-update failed:', response.statusText);
      }
    } catch (error) {
      console.error('Error during auto-update:', error);
    } finally {
      isRunningRef.current = false;
    }
  };

  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Run initial update after 10 seconds
    const initialTimeout = setTimeout(() => {
      runAutoUpdate();
    }, 10000);

    // Set up recurring updates
    intervalRef.current = setInterval(() => {
      runAutoUpdate();
    }, intervalMinutes * 60 * 1000);

    console.log(`Auto-update service started: updating every ${intervalMinutes} minute(s)`);

    return () => {
      clearTimeout(initialTimeout);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      console.log('Auto-update service stopped');
    };
  }, [enabled, intervalMinutes]);

  // This component doesn't render anything
  return null;
}
