'use client';

import React from 'react';
import { Info, TrendingUp, TrendingDown } from 'lucide-react';
import { METRIC_DEFINITIONS, getMetricRanking, formatMetricValue, MetricRanking } from '@/lib/metricRankings';

interface EnhancedMetricDisplayProps {
  metricKey: string;
  value: number;
  className?: string;
  showRank?: boolean;
  showTooltip?: boolean;
}

// Rank badge component
function RankBadge({ ranking }: { ranking: MetricRanking }) {
  return (
    <div className={`inline-flex items-center px-2 py-1 text-xs font-bold border ${ranking.color} ${ranking.bgColor} ${ranking.border}`}>
      {ranking.rank}
    </div>
  );
}

// Tooltip component
function MetricTooltip({ metricKey, ranking }: { metricKey: string; ranking: MetricRanking | null }) {
  const metric = METRIC_DEFINITIONS[metricKey];
  if (!metric) return null;

  return (
    <div className="group relative inline-block">
      <Info className="h-4 w-4 text-gray-400 hover:text-blue-600 cursor-help ml-1" />
      <div className="invisible group-hover:visible absolute z-50 w-80 p-4 bg-gray-900 text-white text-sm rounded-lg shadow-lg -top-2 left-6 transform">
        <div className="font-semibold mb-2 text-blue-300">{metric.name}</div>
        <div className="text-gray-200 mb-3">{metric.explanation}</div>
        
        {ranking && (
          <div className="border-t border-gray-700 pt-2">
            <div className="flex items-center justify-between mb-1">
              <span className="text-gray-300">Current Rating:</span>
              <div className={`px-2 py-1 rounded text-xs font-bold ${ranking.color} ${ranking.bgColor}`}>
                {ranking.rank} Rank
              </div>
            </div>
            <div className="text-gray-300 text-xs">{ranking.description}</div>
          </div>
        )}
        
        <div className="absolute top-2 -left-1 w-2 h-2 bg-gray-900 transform rotate-45"></div>
      </div>
    </div>
  );
}

// Trend indicator component
function TrendIndicator({ isGood, value }: { isGood: boolean; value: number }) {
  if (value === 0) return null;
  
  return (
    <div className={`inline-flex items-center ml-2 ${isGood ? 'text-green-600' : 'text-red-600'}`}>
      {isGood ? (
        <TrendingUp className="h-3 w-3" />
      ) : (
        <TrendingDown className="h-3 w-3" />
      )}
    </div>
  );
}

export default function EnhancedMetricDisplay({
  metricKey,
  value,
  className = '',
  showRank = true,
  showTooltip = true
}: EnhancedMetricDisplayProps) {
  const metric = METRIC_DEFINITIONS[metricKey];
  const ranking = getMetricRanking(metricKey, value);
  const formattedValue = formatMetricValue(metricKey, value);

  if (!metric) {
    return (
      <div className={`text-gray-500 ${className}`}>
        {formattedValue}
      </div>
    );
  }

  // Determine if this is a good or bad value
  const isGoodValue = ranking?.isGood ?? true;
  
  // Color based on whether it's good or bad
  const valueColor = isGoodValue ? 'text-green-700' : 'text-red-700';
  const bgColor = isGoodValue ? 'bg-green-50' : 'bg-red-50';
  const borderColor = isGoodValue ? 'border-green-200' : 'border-red-200';

  return (
    <div className={`flex items-center justify-between p-3 border ${bgColor} ${borderColor} ${className}`}>
      <div className="flex items-center">
        <div>
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-700">{metric.name}</span>
            {showTooltip && <MetricTooltip metricKey={metricKey} ranking={ranking} />}
          </div>
          <div className="flex items-center mt-1">
            <span className={`text-lg font-bold ${valueColor}`}>
              {formattedValue}
            </span>
            <TrendIndicator isGood={isGoodValue} value={value} />
          </div>
        </div>
      </div>

      {showRank && ranking && (
        <div className="flex flex-col items-end">
          <RankBadge ranking={ranking} />
          <div className="text-xs text-gray-500 mt-1 text-right">
            {ranking.description}
          </div>
        </div>
      )}
    </div>
  );
}

// Grid component for displaying multiple metrics
interface MetricGridProps {
  metrics: Array<{
    key: string;
    value: number;
  }>;
  className?: string;
}

export function MetricGrid({ metrics, className = '' }: MetricGridProps) {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 gap-4 ${className}`}>
      {metrics.map(({ key, value }) => (
        <EnhancedMetricDisplay
          key={key}
          metricKey={key}
          value={value}
          showRank={true}
          showTooltip={true}
        />
      ))}
    </div>
  );
}

// Summary component showing overall metric health
interface MetricSummaryProps {
  metrics: Array<{
    key: string;
    value: number;
  }>;
  className?: string;
}

export function MetricSummary({ metrics, className = '' }: MetricSummaryProps) {
  const rankings = metrics
    .map(({ key, value }) => getMetricRanking(key, value))
    .filter(Boolean) as MetricRanking[];

  if (rankings.length === 0) {
    return null;
  }

  const rankCounts = rankings.reduce((acc, ranking) => {
    acc[ranking.rank] = (acc[ranking.rank] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const goodMetrics = rankings.filter(r => r.isGood).length;
  const totalMetrics = rankings.length;
  const healthPercentage = Math.round((goodMetrics / totalMetrics) * 100);

  const getHealthColor = () => {
    if (healthPercentage >= 80) return 'text-green-600 bg-green-50 border-green-200';
    if (healthPercentage >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  return (
    <div className={`p-4 border ${getHealthColor()} ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h4 className="font-semibold">Metric Health Score</h4>
        <span className="text-2xl font-bold">{healthPercentage}%</span>
      </div>

      <div className="flex flex-wrap gap-2 mb-3">
        {Object.entries(rankCounts).map(([rank, count]) => {
          const rankColor = METRIC_DEFINITIONS.returnOnEquity.getRanking(0);
          return (
            <div key={rank} className="flex items-center text-xs">
              <div className={`w-3 h-3 mr-1 ${rank === 'S' ? 'bg-purple-500' :
                rank === 'A' ? 'bg-green-500' :
                rank === 'B' ? 'bg-blue-500' :
                rank === 'C' ? 'bg-yellow-500' :
                rank === 'D' ? 'bg-orange-500' : 'bg-red-500'}`}>
              </div>
              <span>{rank}: {count}</span>
            </div>
          );
        })}
      </div>

      <div className="text-sm">
        {goodMetrics} of {totalMetrics} metrics show positive indicators
      </div>
    </div>
  );
}
