'use client';

import React from 'react';

interface MetricRanking {
  rank: string;
  description: string;
  isGood: boolean;
}

interface MetricWithRanking {
  value: number;
  ranking: MetricRanking | null;
}

interface FinancialMetrics {
  returnOnEquity: MetricWithRanking;
  debtToEquity: MetricWithRanking;
  earningsGrowth: MetricWithRanking;
  salesGrowth: MetricWithRanking;
  pegRatio: MetricWithRanking;
  profitMargins: MetricWithRanking;
  currentRatio: MetricWithRanking;
}

interface FinancialMetricsDisplayProps {
  metrics: FinancialMetrics | null;
  symbol: string;
  compact?: boolean;
}

export default function FinancialMetricsDisplay({ 
  metrics, 
  symbol, 
  compact = false 
}: FinancialMetricsDisplayProps) {
  
  const formatValue = (metric: MetricWithRanking, unit: string) => {
    if (unit === '%') {
      return `${metric.value.toFixed(1)}%`;
    } else if (unit === 'ratio') {
      return metric.value.toFixed(2);
    }
    return metric.value.toString();
  };

  const getRankColor = (ranking: MetricRanking | null) => {
    if (!ranking) return 'text-gray-400 bg-gray-50 border-gray-200';
    
    if (ranking.isGood) {
      switch (ranking.rank) {
        case 'S': return 'text-green-800 bg-green-100 border-green-300';
        case 'A': return 'text-green-700 bg-green-50 border-green-200';
        case 'B': return 'text-blue-700 bg-blue-50 border-blue-200';
        default: return 'text-gray-600 bg-gray-50 border-gray-200';
      }
    } else {
      switch (ranking.rank) {
        case 'D': return 'text-orange-700 bg-orange-50 border-orange-200';
        case 'E': return 'text-red-700 bg-red-50 border-red-200';
        default: return 'text-yellow-700 bg-yellow-50 border-yellow-200';
      }
    }
  };

  const renderMetric = (
    metric: MetricWithRanking,
    label: string,
    unit: string,
    description: string
  ) => {
    if (compact) {
      return (
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">{label}:</span>
          <div className="flex items-center space-x-2">
            <span className="font-medium">{formatValue(metric, unit)}</span>
            {metric.ranking && (
              <div className={`px-2 py-1 text-xs font-bold border ${getRankColor(metric.ranking)}`}>
                {metric.ranking.rank}
              </div>
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="p-4 bg-white border border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-semibold text-gray-900">{label}</h4>
          {metric.ranking && (
            <div className={`px-3 py-1 text-sm font-bold border ${getRankColor(metric.ranking)}`}>
              {metric.ranking.rank}
            </div>
          )}
        </div>
        <div className="text-2xl font-bold text-gray-900 mb-1">
          {formatValue(metric, unit)}
        </div>
        <div className="text-sm text-gray-600 mb-2">
          {description}
        </div>
        {metric.ranking && (
          <div className={`text-sm ${metric.ranking.isGood ? 'text-green-600' : 'text-red-600'}`}>
            {metric.ranking.description}
          </div>
        )}
      </div>
    );
  };

  if (!metrics) {
    return (
      <div className={`${compact ? 'p-3' : 'p-6'} bg-gray-50 border border-gray-200 text-center`}>
        <div className="text-gray-500">
          <div className="text-lg font-medium mb-2">Financial Metrics Unavailable</div>
          <div className="text-sm">
            Real financial data integration required for {symbol}
          </div>
          <div className="text-xs mt-2 text-gray-400">
            Connect to Alpha Vantage, Yahoo Finance, or Polygon.io for live data
          </div>
        </div>
      </div>
    );
  }

  if (compact) {
    return (
      <div className="space-y-2">
        <div className="text-sm font-semibold text-gray-700 mb-3">Financial Metrics</div>
        {renderMetric(metrics.returnOnEquity, 'Return on Equity (ROE)', '%', 'Profitability efficiency')}
        {renderMetric(metrics.debtToEquity, 'Debt-to-Equity Ratio', 'ratio', 'Financial stability')}
        {renderMetric(metrics.earningsGrowth, 'Earnings Growth', '%', 'Profit growth rate')}
        {renderMetric(metrics.salesGrowth, 'Sales Growth', '%', 'Revenue growth rate')}
        {renderMetric(metrics.pegRatio, 'PEG Ratio', 'ratio', 'Value for growth')}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-bold text-gray-900 mb-4">Financial Metrics for {symbol}</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {renderMetric(
          metrics.returnOnEquity,
          'Return on Equity (ROE)',
          '%',
          'Measures how effectively a company uses shareholders\' equity to generate profits'
        )}
        
        {renderMetric(
          metrics.debtToEquity,
          'Debt-to-Equity Ratio',
          'ratio',
          'Compares total debt to shareholders\' equity, indicating financial leverage'
        )}
        
        {renderMetric(
          metrics.earningsGrowth,
          'Earnings Growth',
          '%',
          'Year-over-year growth in company earnings'
        )}
        
        {renderMetric(
          metrics.salesGrowth,
          'Sales Growth',
          '%',
          'Year-over-year growth in company revenue'
        )}
        
        {renderMetric(
          metrics.pegRatio,
          'PEG Ratio',
          'ratio',
          'Price/Earnings ratio divided by earnings growth rate'
        )}
        
        {renderMetric(
          metrics.profitMargins,
          'Profit Margin',
          '%',
          'Net income as a percentage of revenue'
        )}
        
        {renderMetric(
          metrics.currentRatio,
          'Current Ratio',
          'ratio',
          'Current assets divided by current liabilities'
        )}
      </div>
    </div>
  );
}
