'use client';

import { useState, useEffect, useRef } from 'react';
import { Search, TrendingUp, Clock, Star, ChevronRight } from 'lucide-react';
import { SearchService } from '@/lib/searchService';
import { StockRecommendation } from '@/lib/recommendationEngine';
import StockDetailModal from './StockDetailModal';
import HorizontalStockList from './HorizontalStockList';

interface SearchSuggestion {
  symbol: string;
  name: string;
  type: 'stock' | 'suggestion';
  price?: number;
  change?: number;
  changePercent?: number;
}

export default function GoogleLikeStockSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedStock, setSelectedStock] = useState<StockRecommendation | null>(null);
  const [trendingStocks, setTrendingStocks] = useState<StockRecommendation[]>([]);
  const [popularStocks, setPopularStocks] = useState<StockRecommendation[]>([]);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  
  const searchInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Load search history from cookies
  useEffect(() => {
    const getCookie = (name: string) => {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) return parts.pop()?.split(';').shift();
      return null;
    };

    const history = getCookie('stockSearchHistory');
    if (history) {
      try {
        setSearchHistory(JSON.parse(decodeURIComponent(history)));
      } catch (error) {
        console.error('Error parsing search history from cookie:', error);
      }
    }
  }, []);

  // Load trending and popular stocks
  useEffect(() => {
    const loadStocks = async () => {
      try {
        // Get trending stocks
        const trending = await SearchService.getTrendingStocks();
        setTrendingStocks(trending.slice(0, 6));

        // Get popular stocks from API
        try {
          const response = await fetch('/api/popularity?limit=8');
          const data = await response.json();

          if (data.topStocks && data.topStocks.length > 0) {
            // Get analysis for popular stocks
            const popularWithAnalysis = await Promise.all(
              data.topStocks.map(async (item: { symbol: string; popularity: number }) => {
                try {
                  return await SearchService.getStockAnalysis(item.symbol);
                } catch (error) {
                  console.warn(`Failed to analyze popular stock ${item.symbol}:`, error);
                  return null;
                }
              })
            );

            const validPopular = popularWithAnalysis.filter(stock => stock !== null);
            setPopularStocks(validPopular.slice(0, 8));
          } else {
            // Fallback to SearchService
            const popular = await SearchService.getPopularStocks();
            setPopularStocks(popular.slice(0, 8));
          }
        } catch (error) {
          console.warn('Failed to load popular stocks from API, using fallback:', error);
          const popular = await SearchService.getPopularStocks();
          setPopularStocks(popular.slice(0, 8));
        }
      } catch (error) {
        console.error('Error loading stocks:', error);
      }
    };

    loadStocks();
  }, []);

  // Handle search input changes
  useEffect(() => {
    const searchStocks = async () => {
      if (searchQuery.trim().length < 2) {
        setSuggestions([]);
        setShowSuggestions(false);
        return;
      }

      setIsLoading(true);
      try {
        const results = await SearchService.searchStocks(searchQuery);
        const formattedSuggestions: SearchSuggestion[] = results.map(result => ({
          symbol: result.symbol,
          name: result.name,
          type: 'stock' as const,
          price: result.price,
          change: result.change,
          changePercent: result.changePercent
        }));
        
        setSuggestions(formattedSuggestions);
        setShowSuggestions(true);
      } catch (error) {
        console.error('Search error:', error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchStocks, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  // Handle stock selection with real-time price update
  const handleStockSelect = async (symbol: string) => {
    try {
      // Add to search history
      const newHistory = [symbol, ...searchHistory.filter(s => s !== symbol)].slice(0, 10);
      setSearchHistory(newHistory);

      // Save to cookie (expires in 30 days)
      const setCookie = (name: string, value: string, days: number) => {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${encodeURIComponent(value)};expires=${expires.toUTCString()};path=/`;
      };

      setCookie('stockSearchHistory', JSON.stringify(newHistory), 30);

      // Get real-time price update for this stock
      try {
        console.log(`Fetching real-time price for ${symbol}...`);
        const priceResponse = await fetch('/api/stocks/realtime-price', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ symbol }),
        });

        if (priceResponse.ok) {
          const priceData = await priceResponse.json();
          console.log(`Real-time price updated for ${symbol}: $${priceData.price} (${priceData.responseTime}ms)`);
        }
      } catch (priceError) {
        console.warn('Failed to get real-time price:', priceError);
      }

      // Increment popularity via API
      try {
        await fetch('/api/popularity', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ symbol }),
        });
      } catch (error) {
        console.warn('Failed to increment popularity:', error);
      }

      // Get detailed stock analysis
      const stockData = await SearchService.getStockAnalysis(symbol);
      if (stockData) {
        setSelectedStock(stockData);
      }

      setSearchQuery('');
      setShowSuggestions(false);
    } catch (error) {
      console.error('Error selecting stock:', error);
    }
  };

  // Handle clicking outside suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node) &&
          searchInputRef.current && !searchInputRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const formatPrice = (price: number) => `$${price.toFixed(2)}`;
  const formatChange = (change: number, changePercent: number) => {
    const isPositive = change >= 0;
    return (
      <span className={`text-sm ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? '+' : ''}{formatPrice(change)} ({isPositive ? '+' : ''}{changePercent.toFixed(2)}%)
      </span>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-40 shadow-sm">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center space-x-4">
            <div className="bg-blue-600 p-2 rounded-sm">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                StockSearch
              </h1>
              <div className="text-xs text-gray-600 font-medium">Professional Stock Analysis</div>
            </div>
          </div>
          <div className="hidden md:flex items-center space-x-6">
            <div className="text-sm text-gray-700 font-medium">Live Market Data</div>
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          </div>
        </div>
      </header>

      {/* Main Search Section */}
      <div className="flex flex-col items-center justify-center min-h-[60vh] px-6 bg-white">
        <div className="w-full max-w-4xl">
          {/* Logo and Title */}
          <div className="text-center mb-16">
            <div className="flex items-center justify-center mb-8">
              <div className="bg-blue-600 p-4 rounded-sm">
                <TrendingUp className="h-12 w-12 text-white" />
              </div>
            </div>
            <h1 className="text-5xl font-bold text-gray-900 mb-6">
              Professional Stock Analysis
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Advanced financial metrics and AI-powered insights for informed investment decisions
            </p>
          </div>

          {/* Search Box */}
          <div className="relative max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onFocus={() => searchQuery.length >= 2 && setShowSuggestions(true)}
                placeholder="Enter stock symbol or company name..."
                className="w-full pl-12 pr-4 py-4 text-lg border-2 border-gray-300 rounded-sm focus:outline-none focus:border-blue-600 transition-colors duration-200 bg-white"
              />
              {isLoading && (
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                </div>
              )}
            </div>

            {/* Search Suggestions */}
            {showSuggestions && suggestions.length > 0 && (
              <div
                ref={suggestionsRef}
                className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-sm shadow-lg z-50 max-h-96 overflow-y-auto"
              >
                {suggestions.map((suggestion, index) => (
                  <button
                    key={`${suggestion.symbol}-${index}`}
                    onClick={() => handleStockSelect(suggestion.symbol)}
                    className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center justify-between border-b border-gray-100 last:border-b-0 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-600 rounded-sm flex items-center justify-center">
                        <span className="text-white font-semibold text-xs">{suggestion.symbol.slice(0, 2)}</span>
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900">{suggestion.symbol}</div>
                        <div className="text-sm text-gray-600">{suggestion.name}</div>
                      </div>
                    </div>
                    {suggestion.price && (
                      <div className="text-right">
                        <div className="font-semibold text-gray-900">{formatPrice(suggestion.price)}</div>
                        {suggestion.change !== undefined && suggestion.changePercent !== undefined && (
                          <div>{formatChange(suggestion.change, suggestion.changePercent)}</div>
                        )}
                      </div>
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Search History */}
          {searchHistory.length > 0 && !showSuggestions && (
            <div className="mt-8">
              <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                Recent Searches
              </h3>
              <div className="flex flex-wrap gap-2">
                {searchHistory.slice(0, 5).map((symbol) => (
                  <button
                    key={symbol}
                    onClick={() => handleStockSelect(symbol)}
                    className="px-3 py-2 bg-gray-200 text-gray-800 rounded-sm text-sm hover:bg-gray-300 transition-colors font-medium"
                  >
                    {symbol}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Trending Stocks Section */}
      <div className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-16">
          <div className="mb-12">
            <div className="flex items-center mb-4">
              <TrendingUp className="h-6 w-6 text-green-600 mr-3" />
              <h2 className="text-3xl font-bold text-gray-900">
                Trending Stocks
              </h2>
            </div>
            <p className="text-lg text-gray-600">Most analyzed stocks in the last 24 hours</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
            {trendingStocks.map((stock) => (
              <button
                key={stock.symbol}
                onClick={() => handleStockSelect(stock.symbol)}
                className="p-6 bg-white border border-gray-200 rounded-sm hover:shadow-lg transition-all duration-200 text-left group"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-blue-600 rounded-sm flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{stock.symbol.slice(0, 2)}</span>
                    </div>
                    <div>
                      <div className="font-bold text-gray-900">{stock.symbol}</div>
                      <div className="text-sm text-gray-600">{stock.companyName}</div>
                    </div>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-xl font-bold text-gray-900 mb-1">{formatPrice(stock.currentPrice)}</div>
                    <div className="text-sm text-gray-600">
                      Score: <span className="font-semibold text-blue-600">{stock.score}/10</span>
                    </div>
                  </div>
                  <div className="flex flex-col items-end space-y-2">
                    <div className={`px-3 py-1 rounded-sm text-sm font-semibold ${
                      stock.recommendation === 'BUY' ? 'bg-green-600 text-white' :
                      stock.recommendation === 'HOLD' ? 'bg-yellow-600 text-white' :
                      'bg-red-600 text-white'
                    }`}>
                      {stock.recommendation}
                    </div>
                    <div className={`text-xs px-2 py-1 rounded-sm font-medium ${
                      stock.category === 'Lower Risk' ? 'bg-blue-100 text-blue-800' :
                      stock.category === 'Balanced Risk' ? 'bg-purple-100 text-purple-800' :
                      'bg-orange-100 text-orange-800'
                    }`}>
                      {stock.category}
                    </div>
                  </div>
                </div>
              </button>
          ))}
        </div>

          {/* Popular Stocks Section */}
          <div className="mb-12">
            <div className="flex items-center mb-4">
              <Star className="h-6 w-6 text-yellow-600 mr-3" />
              <h2 className="text-3xl font-bold text-gray-900">
                Most Popular
              </h2>
            </div>
            <p className="text-lg text-gray-600">Frequently analyzed stocks on our platform</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {popularStocks.map((stock) => (
              <button
                key={stock.symbol}
                onClick={() => handleStockSelect(stock.symbol)}
                className="p-4 bg-white border border-gray-200 rounded-sm hover:shadow-md transition-all duration-200 text-center group"
              >
                <div className="font-bold text-gray-900 mb-1">{stock.symbol}</div>
                <div className="text-sm text-gray-600 mb-2">{formatPrice(stock.currentPrice)}</div>
                <div className="text-xs text-blue-600 font-semibold mb-2">{stock.score}/10</div>
                <div className={`w-full h-1 rounded-sm ${
                  stock.score >= 7 ? 'bg-green-500' :
                  stock.score >= 5 ? 'bg-yellow-500' :
                  'bg-red-500'
                }`}></div>
              </button>
          ))}
          </div>
        </div>
      </div>

      {/* Horizontal Stock List */}
      <HorizontalStockList onStockSelect={handleStockSelect} />

      {/* Stock Detail Modal */}
      {selectedStock && (
        <StockDetailModal
          stock={selectedStock}
          isOpen={!!selectedStock}
          onClose={() => setSelectedStock(null)}
        />
      )}
    </div>
  );
}
