'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, TrendingUp, TrendingDown, Building2, Shield, BarChart3, Zap } from 'lucide-react';

interface MetricWithRanking {
  value: number;
  ranking: {
    rank: string;
    description: string;
    isGood: boolean;
  } | null;
}

interface Stock {
  symbol: string;
  name: string;
  sector?: string;
  price?: number;
  change?: number;
  changePercent?: number;
  score?: number;
  recommendation?: string;
  category?: string;
  riskLevel?: string;
  metrics?: {
    returnOnEquity: MetricWithRanking;
    debtToEquity: MetricWithRanking;
    earningsGrowth: MetricWithRanking;
    salesGrowth: MetricWithRanking;
    pegRatio: MetricWithRanking;
    profitMargins: MetricWithRanking;
    currentRatio: MetricWithRanking;
  } | null;
}

interface StockCategories {
  sectorCategories: Record<string, Stock[]>;
  investmentCategories?: Record<string, Stock[]>;
  total: number;
}

interface HorizontalStockListProps {
  onStockSelect: (symbol: string) => void;
}

export default function HorizontalStockList({ onStockSelect }: HorizontalStockListProps) {
  const [stockData, setStockData] = useState<StockCategories | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'sectors' | 'investment'>('sectors');

  useEffect(() => {
    fetchStocks();
  }, []);

  const fetchStocks = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/stocks/all?analysis=true');
      const data = await response.json();
      setStockData(data);
    } catch (error) {
      console.error('Error fetching stocks:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(price);
  };

  const formatChange = (change: number, changePercent: number) => {
    const isPositive = change >= 0;
    return (
      <span className={`flex items-center text-xs font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? <TrendingUp className="w-3 h-3 mr-1" /> : <TrendingDown className="w-3 h-3 mr-1" />}
        {isPositive ? '+' : ''}{changePercent.toFixed(2)}%
      </span>
    );
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Lower Risk':
        return <Shield className="w-4 h-4 text-green-600" />;
      case 'Balanced Risk':
        return <BarChart3 className="w-4 h-4 text-yellow-600" />;
      case 'Full Throttle':
        return <Zap className="w-4 h-4 text-red-600" />;
      default:
        return <Building2 className="w-4 h-4 text-gray-600" />;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 7) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 5) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const scrollContainer = (containerId: string, direction: 'left' | 'right') => {
    const container = document.getElementById(containerId);
    if (container) {
      const scrollAmount = 300;
      container.scrollBy({
        left: direction === 'left' ? -scrollAmount : scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  if (loading) {
    return (
      <div className="bg-white border-t border-gray-200 py-8">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading stocks...</span>
          </div>
        </div>
      </div>
    );
  }

  if (!stockData) {
    return null;
  }

  const renderMetricBadge = (metric: MetricWithRanking | undefined, label: string) => {
    if (!metric || !metric.ranking) return null;

    const { rank, isGood } = metric.ranking;
    const colorClass = isGood ? 'text-green-600 bg-green-50 border-green-200' : 'text-red-600 bg-red-50 border-red-200';

    return (
      <div className="flex items-center justify-between text-xs">
        <span className="text-gray-600">{label}:</span>
        <div className={`px-1 py-0.5 border font-bold ${colorClass}`}>
          {rank}
        </div>
      </div>
    );
  };

  const renderStockCard = (stock: Stock) => (
    <div
      key={stock.symbol}
      onClick={() => onStockSelect(stock.symbol)}
      className="flex-shrink-0 w-80 p-4 bg-white border border-gray-200 hover:shadow-md transition-all duration-200 cursor-pointer group"
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 flex items-center justify-center text-white text-xs font-bold">
            {stock.symbol.slice(0, 2)}
          </div>
          <div>
            <div className="font-bold text-gray-900 text-sm">{stock.symbol}</div>
            <div className="text-xs text-gray-500 truncate max-w-[120px]">{stock.name}</div>
          </div>
        </div>
        {stock.category && getCategoryIcon(stock.category)}
      </div>

      {stock.price !== undefined && (
        <div className="mb-3">
          <div className="text-lg font-bold text-gray-900">{formatPrice(stock.price)}</div>
          {stock.changePercent !== undefined && formatChange(stock.change || 0, stock.changePercent)}
        </div>
      )}

      {stock.score !== undefined && (
        <div className="flex items-center justify-between mb-3">
          <div className={`px-2 py-1 text-xs font-bold border ${getScoreColor(stock.score)}`}>
            {stock.score.toFixed(1)}/10
          </div>
          <div className="text-xs text-gray-600">{stock.recommendation}</div>
        </div>
      )}

      {/* Financial Metrics */}
      {stock.metrics ? (
        <div className="space-y-1 pt-2 border-t border-gray-100">
          {renderMetricBadge(stock.metrics.returnOnEquity, 'ROE')}
          {renderMetricBadge(stock.metrics.debtToEquity, 'D/E')}
          {renderMetricBadge(stock.metrics.earningsGrowth, 'Earnings')}
          {renderMetricBadge(stock.metrics.pegRatio, 'PEG')}
        </div>
      ) : (
        <div className="pt-2 border-t border-gray-100">
          <div className="text-xs text-gray-400 italic">
            Financial metrics pending real data integration
          </div>
        </div>
      )}
    </div>
  );

  const renderCategory = (categoryName: string, stocks: Stock[], containerId: string) => (
    <div key={categoryName} className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          {activeTab === 'investment' ? getCategoryIcon(categoryName) : <Building2 className="w-5 h-5 text-gray-600" />}
          <span className="ml-2">{categoryName}</span>
          <span className="ml-2 text-sm text-gray-500">({stocks.length})</span>
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={() => scrollContainer(containerId, 'left')}
            className="p-2 border border-gray-300 hover:bg-gray-50 transition-colors"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          <button
            onClick={() => scrollContainer(containerId, 'right')}
            className="p-2 border border-gray-300 hover:bg-gray-50 transition-colors"
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <div
        id={containerId}
        className="flex space-x-4 overflow-x-auto scrollbar-hide pb-4"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {stocks.map(renderStockCard)}
      </div>
    </div>
  );

  const currentCategories = activeTab === 'sectors' 
    ? stockData.sectorCategories 
    : stockData.investmentCategories || {};

  return (
    <div className="bg-white border-t border-gray-200 py-8">
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">All Stocks</h2>
            <p className="text-gray-600">Browse {stockData.total} stocks by category</p>
          </div>
          
          <div className="flex border border-gray-300">
            <button
              onClick={() => setActiveTab('sectors')}
              className={`px-4 py-2 text-sm font-medium transition-colors ${
                activeTab === 'sectors'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              By Sector
            </button>
            <button
              onClick={() => setActiveTab('investment')}
              className={`px-4 py-2 text-sm font-medium transition-colors ${
                activeTab === 'investment'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              By Risk Level
            </button>
          </div>
        </div>

        <div className="space-y-8">
          {Object.entries(currentCategories).map(([categoryName, stocks], index) =>
            renderCategory(categoryName, stocks, `category-${activeTab}-${index}`)
          )}
        </div>
      </div>
    </div>
  );
}
