'use client';

import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';
import { StockRecommendation } from '@/lib/recommendationEngine';

interface StockChartProps {
  stock: StockRecommendation;
  type?: 'metrics' | 'comparison' | 'risk' | 'performance';
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function StockChart({ stock, type = 'metrics' }: StockChartProps) {
  
  const getMetricsData = () => [
    { name: 'ROE', value: stock.metrics.returnOnEquity, target: 15 },
    { name: 'Revenue Growth', value: stock.metrics.salesGrowth1Yr, target: 10 },
    { name: 'Earnings Growth', value: stock.metrics.earningsGrowth1Yr, target: 15 },
    { name: 'Debt/Equity', value: stock.metrics.debtToEquity * 10, target: 5 }, // Scaled for visualization
    { name: 'PEG Ratio', value: stock.metrics.pegRatio * 10, target: 20 }, // Scaled for visualization
  ];

  const getRiskData = () => [
    { name: 'Financial Stability', value: stock.metrics.debtToEquity < 1 ? 85 : 60 },
    { name: 'Growth Potential', value: stock.metrics.salesGrowth1Yr > 10 ? 80 : 50 },
    { name: 'Profitability', value: stock.metrics.returnOnEquity > 15 ? 90 : 60 },
    { name: 'Valuation', value: stock.metrics.pegRatio < 2 ? 75 : 45 },
    { name: 'Market Position', value: stock.score * 10 },
  ];

  const getScoreBreakdown = () => [
    { name: 'Valuation', value: stock.score >= 8 ? 85 : stock.score >= 6 ? 65 : 45 },
    { name: 'Growth', value: stock.metrics.salesGrowth1Yr > 10 ? 80 : 50 },
    { name: 'Financial Health', value: stock.metrics.debtToEquity < 1 ? 90 : 60 },
    { name: 'Profitability', value: stock.metrics.returnOnEquity > 15 ? 85 : 55 },
  ];

  const getPerformanceData = () => [
    { period: '1M', value: Math.random() * 20 - 10 },
    { period: '3M', value: Math.random() * 30 - 15 },
    { period: '6M', value: Math.random() * 40 - 20 },
    { period: '1Y', value: stock.metrics.salesGrowth1Yr },
    { period: '3Y', value: stock.metrics.salesGrowth5Yr / 2 },
    { period: '5Y', value: stock.metrics.salesGrowth5Yr },
  ];

  const renderMetricsChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={getMetricsData()}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip 
          formatter={(value: number, name: string) => [
            `${value.toFixed(1)}${name.includes('Growth') || name.includes('ROE') ? '%' : ''}`,
            name === 'value' ? 'Current' : 'Target'
          ]}
        />
        <Bar dataKey="value" fill="#3B82F6" />
        <Bar dataKey="target" fill="#E5E7EB" />
      </BarChart>
    </ResponsiveContainer>
  );

  const renderRiskChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <RadarChart data={getRiskData()}>
        <PolarGrid />
        <PolarAngleAxis dataKey="name" />
        <PolarRadiusAxis angle={90} domain={[0, 100]} />
        <Radar
          name="Risk Assessment"
          dataKey="value"
          stroke="#3B82F6"
          fill="#3B82F6"
          fillOpacity={0.3}
        />
        <Tooltip />
      </RadarChart>
    </ResponsiveContainer>
  );

  const renderComparisonChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie
          data={getScoreBreakdown()}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, value }) => `${name}: ${value}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {getScoreBreakdown().map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip />
      </PieChart>
    </ResponsiveContainer>
  );

  const renderPerformanceChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={getPerformanceData()}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="period" />
        <YAxis />
        <Tooltip formatter={(value: number) => [`${value.toFixed(1)}%`, 'Growth']} />
        <Line 
          type="monotone" 
          dataKey="value" 
          stroke="#3B82F6" 
          strokeWidth={2}
          dot={{ fill: '#3B82F6' }}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  const getChartTitle = () => {
    switch (type) {
      case 'metrics':
        return 'Key Financial Metrics';
      case 'risk':
        return 'Risk Assessment';
      case 'comparison':
        return 'Score Breakdown';
      case 'performance':
        return 'Performance Trends';
      default:
        return 'Stock Analysis';
    }
  };

  const getChartDescription = () => {
    switch (type) {
      case 'metrics':
        return 'Comparison of current metrics vs. target benchmarks';
      case 'risk':
        return 'Multi-dimensional risk analysis across key factors';
      case 'comparison':
        return 'Breakdown of overall score by category';
      case 'performance':
        return 'Historical performance trends over different periods';
      default:
        return 'Visual analysis of stock data';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{getChartTitle()}</h3>
        <p className="text-sm text-gray-600">{getChartDescription()}</p>
      </div>
      
      <div className="w-full">
        {type === 'metrics' && renderMetricsChart()}
        {type === 'risk' && renderRiskChart()}
        {type === 'comparison' && renderComparisonChart()}
        {type === 'performance' && renderPerformanceChart()}
      </div>

      {/* Chart Legend */}
      <div className="mt-4 text-xs text-gray-500">
        <p>
          {type === 'metrics' && 'Blue bars show current values, gray bars show target benchmarks'}
          {type === 'risk' && 'Higher values indicate better performance in each risk category'}
          {type === 'comparison' && 'Percentage breakdown of overall investment score'}
          {type === 'performance' && 'Growth percentages over different time periods'}
        </p>
      </div>
    </div>
  );
}

// Multi-chart dashboard component
interface StockChartDashboardProps {
  stock: StockRecommendation;
}

export function StockChartDashboard({ stock }: StockChartDashboardProps) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <StockChart stock={stock} type="metrics" />
      <StockChart stock={stock} type="risk" />
      <StockChart stock={stock} type="comparison" />
      <StockChart stock={stock} type="performance" />
    </div>
  );
}

// Compact chart for cards
interface CompactStockChartProps {
  stock: StockRecommendation;
  height?: number;
}

export function CompactStockChart({ stock, height = 100 }: CompactStockChartProps) {
  const data = [
    { name: 'Score', value: stock.score * 10 },
    { name: 'Growth', value: Math.min(stock.metrics.salesGrowth1Yr, 50) },
    { name: 'ROE', value: Math.min(stock.metrics.returnOnEquity, 50) },
  ];

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart data={data} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
        <Bar dataKey="value" fill="#3B82F6" radius={[2, 2, 0, 0]} />
        <Tooltip 
          formatter={(value: number, name: string) => [
            `${value.toFixed(1)}${name === 'Score' ? '/100' : '%'}`,
            name
          ]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
}
