'use client';

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X, TrendingUp, TrendingDown, DollarSign, BarChart3, Shield, Zap, AlertTriangle, Info } from 'lucide-react';
import { StockRecommendation } from '@/lib/recommendationEngine';
import { StockChartDashboard } from './StockChart';
import StockUpdateStatus from './StockUpdateStatus';
import { MetricGrid, MetricSummary } from './EnhancedMetricDisplay';

interface StockDetailModalProps {
  stock: StockRecommendation;
  isOpen: boolean;
  onClose: () => void;
}

// Tooltip component for metric explanations
function MetricTooltip({ title, explanation }: { title: string; explanation: string }) {
  return (
    <div className="group relative inline-block">
      <Info className="h-4 w-4 text-gray-400 hover:text-blue-600 cursor-help ml-1" />
      <div className="invisible group-hover:visible absolute z-50 w-80 p-3 bg-gray-900 text-white text-sm rounded-lg shadow-lg -top-2 left-6 transform">
        <div className="font-semibold mb-1">{title}</div>
        <div className="text-gray-200">{explanation}</div>
        <div className="absolute top-2 -left-1 w-2 h-2 bg-gray-900 transform rotate-45"></div>
      </div>
    </div>
  );
}

// Metric comparison bar component
function MetricComparisonBar({
  value,
  min,
  max,
  optimal,
  label,
  warning,
  context
}: {
  value: number;
  min: number;
  max: number;
  optimal: number;
  label: string;
  warning?: string;
  context: string;
}) {
  const percentage = Math.min(Math.max(((value - min) / (max - min)) * 100, 0), 100);
  const optimalPercentage = ((optimal - min) / (max - min)) * 100;

  const getColor = () => {
    const distance = Math.abs(value - optimal);
    const range = max - min;
    const normalizedDistance = distance / range;

    if (normalizedDistance < 0.1) return 'bg-green-500';
    if (normalizedDistance < 0.3) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getTextColor = () => {
    const distance = Math.abs(value - optimal);
    const range = max - min;
    const normalizedDistance = distance / range;

    if (normalizedDistance < 0.1) return 'text-green-700';
    if (normalizedDistance < 0.3) return 'text-yellow-700';
    return 'text-red-700';
  };

  return (
    <div className="mb-4">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-700">{label}</span>
        <span className={`text-sm font-semibold ${getTextColor()}`}>
          {value.toFixed(2)}
        </span>
      </div>

      <div className="relative">
        {/* Background bar */}
        <div className="w-full h-3 bg-gray-200 rounded-full">
          {/* Value indicator */}
          <div
            className={`h-3 rounded-full transition-all duration-500 ${getColor()}`}
            style={{ width: `${percentage}%` }}
          ></div>

          {/* Optimal point indicator */}
          <div
            className="absolute top-0 w-1 h-3 bg-blue-600 rounded-full"
            style={{ left: `${optimalPercentage}%` }}
            title="Optimal range"
          ></div>
        </div>

        {/* Scale labels */}
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>{min}</span>
          <span className="text-blue-600 font-medium">Optimal: {optimal}</span>
          <span>{max}</span>
        </div>
      </div>

      {/* Context and warning */}
      <div className="mt-2">
        <p className="text-xs text-gray-600">{context}</p>
        {warning && (
          <div className="flex items-center mt-1">
            <AlertTriangle className="h-3 w-3 text-orange-500 mr-1" />
            <p className="text-xs text-orange-600 font-medium">{warning}</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default function StockDetailModal({ stock, isOpen, onClose }: StockDetailModalProps) {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Lower Risk':
        return <Shield className="h-5 w-5" />;
      case 'Balanced Risk':
        return <BarChart3 className="h-5 w-5" />;
      case 'Full Throttle':
        return <Zap className="h-5 w-5" />;
      default:
        return <BarChart3 className="h-5 w-5" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Lower Risk':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'Balanced Risk':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'Full Throttle':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600 bg-green-50';
    if (score >= 6) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'BUY':
        return 'text-green-700 bg-green-100 border-green-300';
      case 'HOLD':
        return 'text-yellow-700 bg-yellow-100 border-yellow-300';
      case 'SELL':
        return 'text-red-700 bg-red-100 border-red-300';
      default:
        return 'text-gray-700 bg-gray-100 border-gray-300';
    }
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const getRecommendationDescription = (recommendation: string, score: number) => {
    switch (recommendation) {
      case 'BUY':
        return `Strong buy recommendation with a score of ${score}/10. This stock shows excellent fundamentals and growth potential.`;
      case 'HOLD':
        return `Hold recommendation with a score of ${score}/10. This stock has decent fundamentals but may not be the best opportunity right now.`;
      case 'SELL':
        return `Sell recommendation with a score of ${score}/10. This stock shows concerning fundamentals and may underperform.`;
      default:
        return `Analysis complete with a score of ${score}/10.`;
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden bg-white p-6 text-left align-middle shadow-xl transition-all border border-gray-200">
                {/* Header */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div>
                      <Dialog.Title as="h3" className="text-2xl font-bold text-gray-900">
                        {stock.symbol}
                      </Dialog.Title>
                      <p className="text-lg text-gray-600">{stock.companyName}</p>
                    </div>
                    <div className={`inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium border ${getCategoryColor(stock.category)}`}>
                      {getCategoryIcon(stock.category)}
                      <span className="ml-2">{stock.category}</span>
                    </div>
                  </div>
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onClick={onClose}
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>

                {/* Score and Recommendation */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div className={`p-4 border ${getScoreColor(stock.score)}`}>
                    <div className="flex items-center">
                      <BarChart3 className="h-6 w-6 mr-2" />
                      <div>
                        <p className="text-sm font-medium">Overall Score</p>
                        <p className="text-2xl font-bold">{stock.score}/10</p>
                      </div>
                    </div>
                  </div>

                  <div className={`p-4 border ${getRecommendationColor(stock.recommendation)}`}>
                    <div className="flex items-center">
                      {stock.recommendation === 'BUY' ? (
                        <TrendingUp className="h-6 w-6 mr-2" />
                      ) : stock.recommendation === 'SELL' ? (
                        <TrendingDown className="h-6 w-6 mr-2" />
                      ) : (
                        <AlertTriangle className="h-6 w-6 mr-2" />
                      )}
                      <div>
                        <p className="text-sm font-medium">Recommendation</p>
                        <p className="text-xl font-bold">{stock.recommendation}</p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border bg-blue-50 text-blue-600 border-blue-200">
                    <div className="flex items-center">
                      <DollarSign className="h-6 w-6 mr-2" />
                      <div>
                        <p className="text-sm font-medium">Current Price</p>
                        <p className="text-xl font-bold">{formatCurrency(stock.currentPrice)}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Update Status Component - Separate row for clean layout */}
                <div className="mb-8">
                  <StockUpdateStatus symbol={stock.symbol} />
                </div>

                {/* Recommendation Description */}
                <div className="mb-8 p-4 bg-gray-50 border border-gray-200">
                  <p className="text-gray-700">
                    {getRecommendationDescription(stock.recommendation, stock.score)}
                  </p>
                </div>

                {/* Criteria Analysis */}
                <div className="mb-8">
                  <div className="flex items-center mb-4">
                    <h4 className="text-lg font-semibold text-gray-900">Criteria Analysis</h4>
                    <MetricTooltip
                      title="Investment Criteria Analysis"
                      explanation="Our AI system evaluates stocks against 9 key investment criteria including P/E ratio, PEG ratio, debt levels, profitability, growth rates, and financial stability. Each criterion is weighted based on its importance for long-term investment success. A score of 70%+ indicates a strong investment candidate, while 50-70% suggests moderate potential."
                    />
                  </div>
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-gray-600">Criteria Passed</span>
                    <span className="font-medium text-gray-900">
                      {stock.criteriaPassedCount}/9 ({stock.criteriaPassedPercentage}%)
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${stock.criteriaPassedPercentage}%` }}
                    ></div>
                  </div>
                </div>

                {/* Enhanced Financial Metrics */}
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-6">
                    <h4 className="text-lg font-semibold text-gray-900">Financial Metrics Analysis</h4>
                    <MetricTooltip
                      title="Enhanced Metric Analysis"
                      explanation="Our enhanced metric system provides letter grades (S, A, B, C, D, E) for each financial indicator. S-rank represents exceptional performance, while E-rank indicates areas of concern. Green colors indicate positive metrics, red colors highlight potential risks."
                    />
                  </div>

                  {/* Metric Summary */}
                  <MetricSummary
                    metrics={[
                      { key: 'returnOnEquity', value: stock.metrics.returnOnEquity },
                      { key: 'debtToEquity', value: stock.metrics.debtToEquity },
                      { key: 'earningsGrowth', value: stock.metrics.earningsGrowth1Yr },
                      { key: 'salesGrowth', value: stock.metrics.salesGrowth1Yr },
                      { key: 'pegRatio', value: stock.metrics.pegRatio }
                    ]}
                    className="mb-6"
                  />

                  {/* Enhanced Metrics Grid */}
                  <MetricGrid
                    metrics={[
                      { key: 'returnOnEquity', value: stock.metrics.returnOnEquity },
                      { key: 'debtToEquity', value: stock.metrics.debtToEquity },
                      { key: 'earningsGrowth', value: stock.metrics.earningsGrowth1Yr },
                      { key: 'salesGrowth', value: stock.metrics.salesGrowth1Yr },
                      { key: 'pegRatio', value: stock.metrics.pegRatio }
                    ]}
                  />

                  {/* Traditional Metrics for Reference */}
                  <div className="mt-6 p-4 bg-gray-50 border border-gray-200">
                    <h5 className="text-sm font-semibold text-gray-700 mb-3">Additional Information</h5>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Market Cap</span>
                        <div className="font-medium text-gray-900">{stock.marketCap}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Free Cash Flow</span>
                        <div className="font-medium text-gray-900">{stock.metrics.freeCashFlow}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">5Y Sales Growth</span>
                        <div className={`font-medium ${stock.metrics.salesGrowth5Yr >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {formatPercentage(stock.metrics.salesGrowth5Yr)}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-600">Risk Level</span>
                        <div className="font-medium text-gray-900">{stock.riskLevel}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Legacy Growth Metrics Section (keeping for compatibility) */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                  <div style={{ display: 'none' }}>
                    {/* Hidden - replaced by enhanced metrics above */}
                  </div>

                  {/* This section has been replaced by the enhanced metrics above */}
                </div>

                {/* Metric Comparisons */}
                <div className="mt-8">
                  <h4 className="text-lg font-semibold text-gray-900 mb-6">Metric Benchmarks</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <MetricComparisonBar
                        value={stock.metrics.returnOnEquity}
                        min={0}
                        max={50}
                        optimal={20}
                        label="Return on Equity (%)"
                        context={`For ${stock.companyName} (Tech sector), ROE above 15% is considered good, above 20% is excellent.`}
                        warning={stock.metrics.returnOnEquity < 10 ? "Below industry average - may indicate inefficient use of equity" : undefined}
                      />

                      <MetricComparisonBar
                        value={stock.metrics.debtToEquity * 100}
                        min={0}
                        max={100}
                        optimal={30}
                        label="Debt-to-Equity (%)"
                        context="Lower is generally better. Tech companies typically maintain lower debt levels."
                        warning={stock.metrics.debtToEquity > 0.6 ? "High debt levels - increased financial risk" : undefined}
                      />

                      <MetricComparisonBar
                        value={stock.metrics.pegRatio}
                        min={0}
                        max={3}
                        optimal={1}
                        label="PEG Ratio"
                        context="PEG ratio of 1.0 suggests fair valuation relative to growth. Below 1.0 may indicate undervaluation."
                        warning={stock.metrics.pegRatio > 2 ? "High PEG ratio - stock may be overvalued relative to growth" : undefined}
                      />
                    </div>

                    <div className="space-y-4">
                      <MetricComparisonBar
                        value={stock.metrics.earningsGrowth1Yr}
                        min={-20}
                        max={50}
                        optimal={15}
                        label="Earnings Growth (%)"
                        context="Strong earnings growth indicates a healthy, expanding business. Tech stocks often target 15-25% growth."
                        warning={stock.metrics.earningsGrowth1Yr < 0 ? "Negative earnings growth - declining profitability" : undefined}
                      />

                      <MetricComparisonBar
                        value={stock.metrics.salesGrowth1Yr}
                        min={-10}
                        max={40}
                        optimal={12}
                        label="Sales Growth (%)"
                        context="Consistent sales growth shows market demand and competitive positioning."
                        warning={stock.metrics.salesGrowth1Yr < 5 ? "Low sales growth - may indicate market saturation or competitive pressure" : undefined}
                      />

                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h5 className="font-semibold text-blue-900 mb-2">Overall Assessment</h5>
                        <p className="text-sm text-blue-800">
                          {stock.score >= 8 ? "Excellent fundamentals with strong metrics across all categories." :
                           stock.score >= 6 ? "Good fundamentals with some areas for improvement." :
                           stock.score >= 4 ? "Mixed fundamentals - careful analysis recommended." :
                           "Weak fundamentals - high risk investment."}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Charts Section */}
                <div className="mt-8">
                  <h4 className="text-lg font-semibold text-gray-900 mb-6">Visual Analysis</h4>
                  <StockChartDashboard stock={stock} />
                </div>

                {/* Disclaimer */}
                <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
                  <p className="text-sm text-yellow-800">
                    <strong>Disclaimer:</strong> This analysis is for informational purposes only and should not be considered as financial advice.
                    Always conduct your own research and consult with a qualified financial advisor before making investment decisions.
                  </p>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
