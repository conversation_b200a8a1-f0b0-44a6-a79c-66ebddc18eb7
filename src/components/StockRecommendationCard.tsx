'use client';

import { TrendingUp, TrendingDown, DollarSign, BarChart3, Shield, Zap } from 'lucide-react';
import { StockRecommendation } from '@/lib/recommendationEngine';
import { CompactStockChart } from './StockChart';

interface StockRecommendationCardProps {
  stock: StockRecommendation;
  onClick: () => void;
}

export default function StockRecommendationCard({ stock, onClick }: StockRecommendationCardProps) {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Lower Risk':
        return <Shield className="h-4 w-4" />;
      case 'Balanced Risk':
        return <BarChart3 className="h-4 w-4" />;
      case 'Full Throttle':
        return <Zap className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Lower Risk':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'Balanced Risk':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'Full Throttle':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600 bg-green-50';
    if (score >= 6) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'BUY':
        return 'text-green-700 bg-green-100 border-green-300';
      case 'HOLD':
        return 'text-yellow-700 bg-yellow-100 border-yellow-300';
      case 'SELL':
        return 'text-red-700 bg-red-100 border-red-300';
      default:
        return 'text-gray-700 bg-gray-100 border-gray-300';
    }
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  return (
    <div
      onClick={onClick}
      className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-200 cursor-pointer hover:border-blue-300 group"
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
            {stock.symbol}
          </h3>
          <p className="text-sm text-gray-600 truncate max-w-[200px]">
            {stock.companyName}
          </p>
        </div>
        <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getCategoryColor(stock.category)}`}>
          {getCategoryIcon(stock.category)}
          <span className="ml-1">{stock.category}</span>
        </div>
      </div>

      {/* Score and Recommendation */}
      <div className="flex items-center justify-between mb-4">
        <div className={`inline-flex items-center px-3 py-1 rounded-lg text-sm font-bold ${getScoreColor(stock.score)}`}>
          <BarChart3 className="h-4 w-4 mr-1" />
          Score: {stock.score}/10
        </div>
        <div className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium border ${getRecommendationColor(stock.recommendation)}`}>
          {stock.recommendation}
        </div>
      </div>

      {/* Price and Market Cap */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <p className="text-sm text-gray-600">Current Price</p>
          <p className="text-lg font-semibold text-gray-900">
            {formatCurrency(stock.currentPrice)}
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-600">Market Cap</p>
          <p className="text-sm font-medium text-gray-900">{stock.marketCap}</p>
        </div>
      </div>

      {/* Criteria Passed */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Criteria Passed</span>
          <span className="text-sm font-medium text-gray-900">
            {stock.criteriaPassedCount}/9 ({stock.criteriaPassedPercentage}%)
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${stock.criteriaPassedPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">ROE:</span>
          <span className="font-medium text-gray-900">
            {formatPercentage(stock.metrics.returnOnEquity)}
          </span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Revenue Growth:</span>
          <span className={`font-medium ${stock.metrics.salesGrowth1Yr >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {formatPercentage(stock.metrics.salesGrowth1Yr)}
          </span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Debt/Equity:</span>
          <span className="font-medium text-gray-900">
            {stock.metrics.debtToEquity.toFixed(2)}
          </span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">PEG Ratio:</span>
          <span className="font-medium text-gray-900">
            {stock.metrics.pegRatio > 0 ? stock.metrics.pegRatio.toFixed(2) : 'N/A'}
          </span>
        </div>
      </div>

      {/* Mini Chart */}
      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="mb-2">
          <span className="text-sm text-gray-600">Performance Overview</span>
        </div>
        <CompactStockChart stock={stock} height={60} />
      </div>

      {/* Risk Level Indicator */}
      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Risk Level:</span>
          <span className={`text-xs font-medium px-2 py-1 rounded ${
            stock.riskLevel === 'LOW' ? 'bg-green-100 text-green-700' :
            stock.riskLevel === 'MEDIUM' ? 'bg-yellow-100 text-yellow-700' :
            'bg-red-100 text-red-700'
          }`}>
            {stock.riskLevel}
          </span>
        </div>
      </div>

      {/* Hover Effect Indicator */}
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-400 group-hover:text-blue-500 transition-colors">
          Click for detailed analysis →
        </p>
      </div>
    </div>
  );
}
