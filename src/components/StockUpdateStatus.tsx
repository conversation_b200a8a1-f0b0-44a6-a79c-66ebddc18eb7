'use client';

import React, { useState, useEffect } from 'react';
import { Clock, RefreshCw, Star, Users, TrendingUp } from 'lucide-react';

interface QueueItem {
  symbol: string;
  priority_score: number;
  user_requests: number;
  last_updated_at: string | null;
  last_requested_at: string | null;
  queue_position: number;
  is_priority: boolean;
}

interface StockUpdateStatusProps {
  symbol: string;
  className?: string;
}

export default function StockUpdateStatus({ symbol, className = '' }: StockUpdateStatusProps) {
  const [updateStatus, setUpdateStatus] = useState<{
    lastUpdated: string | null;
    queuePosition: number | null;
    isRequesting: boolean;
    userRequests: number;
    timeUntilUpdate: string;
  }>({
    lastUpdated: null,
    queuePosition: null,
    isRequesting: false,
    userRequests: 0,
    timeUntilUpdate: ''
  });

  const [queue, setQueue] = useState<QueueItem[]>([]);

  // Fetch current update status
  const fetchUpdateStatus = async () => {
    try {
      const response = await fetch('/api/stocks/update-queue');
      if (response.ok) {
        const data = await response.json();
        const stockInQueue = data.queue.find((item: QueueItem) => item.symbol === symbol);
        
        if (stockInQueue) {
          setUpdateStatus(prev => ({
            ...prev,
            lastUpdated: stockInQueue.last_updated_at,
            queuePosition: stockInQueue.queue_position,
            userRequests: stockInQueue.user_requests
          }));
        }
        
        setQueue(data.queue || []);
      }
    } catch (error) {
      console.error('Error fetching update status:', error);
    }
  };

  // Request priority update
  const requestPriorityUpdate = async () => {
    if (updateStatus.isRequesting) return;

    setUpdateStatus(prev => ({ ...prev, isRequesting: true }));

    try {
      const response = await fetch('/api/stocks/update-queue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ symbol })
      });

      if (response.ok) {
        const data = await response.json();
        setUpdateStatus(prev => ({
          ...prev,
          queuePosition: data.queue_position,
          userRequests: data.user_requests,
          isRequesting: false
        }));
        
        // Refresh the queue
        await fetchUpdateStatus();
      } else {
        console.error('Failed to request priority update');
        setUpdateStatus(prev => ({ ...prev, isRequesting: false }));
      }
    } catch (error) {
      console.error('Error requesting priority update:', error);
      setUpdateStatus(prev => ({ ...prev, isRequesting: false }));
    }
  };

  // Calculate time since last update
  const getTimeSinceUpdate = (lastUpdated: string | null): string => {
    if (!lastUpdated) return 'Never updated';
    
    const now = new Date();
    const updated = new Date(lastUpdated);
    const diffMs = now.getTime() - updated.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) return `${diffDays}d ago`;
    if (diffHours > 0) return `${diffHours}h ago`;
    if (diffMinutes > 0) return `${diffMinutes}m ago`;
    return 'Just updated';
  };

  // Get update status color
  const getStatusColor = (lastUpdated: string | null): string => {
    if (!lastUpdated) return 'text-red-500';
    
    const now = new Date();
    const updated = new Date(lastUpdated);
    const diffHours = (now.getTime() - updated.getTime()) / (1000 * 60 * 60);

    if (diffHours < 1) return 'text-green-500';
    if (diffHours < 6) return 'text-yellow-500';
    if (diffHours < 24) return 'text-orange-500';
    return 'text-red-500';
  };

  // Estimate time until update (rough calculation)
  const getTimeUntilUpdate = (queuePosition: number | null): string => {
    if (!queuePosition) return 'Not in queue';
    if (queuePosition === 1) return 'Next update';
    
    // Assuming 3 stocks per minute with 12-second delays
    const estimatedMinutes = Math.ceil((queuePosition - 1) / 3);
    if (estimatedMinutes < 60) return `~${estimatedMinutes}m`;
    
    const hours = Math.floor(estimatedMinutes / 60);
    const minutes = estimatedMinutes % 60;
    return `~${hours}h ${minutes}m`;
  };

  useEffect(() => {
    fetchUpdateStatus();
    
    // Refresh status every 30 seconds
    const interval = setInterval(fetchUpdateStatus, 30000);
    return () => clearInterval(interval);
  }, [symbol]);

  const timeSinceUpdate = getTimeSinceUpdate(updateStatus.lastUpdated);
  const statusColor = getStatusColor(updateStatus.lastUpdated);
  const timeUntilUpdate = getTimeUntilUpdate(updateStatus.queuePosition);

  return (
    <div className={`bg-white border border-gray-200 ${className}`}>
      {/* Minimal header with clean typography */}
      <div className="flex items-center justify-between px-3 py-2 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <Clock className="w-3 h-3 text-gray-400" />
          <span className="text-xs font-medium text-gray-600">Last Updated</span>
        </div>
        <span className={`text-xs font-medium ${statusColor}`}>
          {timeSinceUpdate}
        </span>
      </div>

      {/* Compact status info */}
      <div className="px-3 py-2">
        <div className="flex items-center justify-between">
          <button
            onClick={requestPriorityUpdate}
            disabled={updateStatus.isRequesting}
            className="flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {updateStatus.isRequesting ? (
              <RefreshCw className="w-3 h-3 animate-spin" />
            ) : (
              <Star className="w-3 h-3" />
            )}
            Priority
          </button>

          {updateStatus.queuePosition && (
            <div className="flex items-center gap-2 text-xs">
              <span className="text-gray-500">Queue:</span>
              <span className="font-medium text-gray-700">#{updateStatus.queuePosition}</span>
              {timeUntilUpdate !== 'Not in queue' && (
                <>
                  <span className="text-gray-400">•</span>
                  <span className="text-gray-500">{timeUntilUpdate}</span>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
