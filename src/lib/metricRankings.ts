// Metric ranking and explanation system

export interface MetricRanking {
  rank: 'S' | 'A' | 'B' | 'C' | 'D' | 'E';
  color: string;
  bgColor: string;
  description: string;
  isGood: boolean;
}

export interface MetricInfo {
  name: string;
  explanation: string;
  unit: string;
  higherIsBetter: boolean;
  getRanking: (value: number) => MetricRanking;
}

// Ranking colors
const RANK_COLORS = {
  S: { color: 'text-purple-700', bgColor: 'bg-purple-100', border: 'border-purple-300' },
  A: { color: 'text-green-700', bgColor: 'bg-green-100', border: 'border-green-300' },
  B: { color: 'text-blue-700', bgColor: 'bg-blue-100', border: 'border-blue-300' },
  C: { color: 'text-yellow-700', bgColor: 'bg-yellow-100', border: 'border-yellow-300' },
  D: { color: 'text-orange-700', bgColor: 'bg-orange-100', border: 'border-orange-300' },
  E: { color: 'text-red-700', bgColor: 'bg-red-100', border: 'border-red-300' }
};

// Return on Equity (ROE) ranking
function getRoeRanking(roe: number): MetricRanking {
  if (roe >= 20) return { rank: 'S', ...RANK_COLORS.S, description: 'Exceptional profitability', isGood: true };
  if (roe >= 15) return { rank: 'A', ...RANK_COLORS.A, description: 'Excellent profitability', isGood: true };
  if (roe >= 10) return { rank: 'B', ...RANK_COLORS.B, description: 'Good profitability', isGood: true };
  if (roe >= 5) return { rank: 'C', ...RANK_COLORS.C, description: 'Average profitability', isGood: false };
  if (roe >= 0) return { rank: 'D', ...RANK_COLORS.D, description: 'Below average profitability', isGood: false };
  return { rank: 'E', ...RANK_COLORS.E, description: 'Poor profitability (negative)', isGood: false };
}

// Debt-to-Equity ranking (lower is better)
function getDebtToEquityRanking(ratio: number): MetricRanking {
  if (ratio <= 0.3) return { rank: 'S', ...RANK_COLORS.S, description: 'Excellent financial stability', isGood: true };
  if (ratio <= 0.5) return { rank: 'A', ...RANK_COLORS.A, description: 'Very good financial stability', isGood: true };
  if (ratio <= 1.0) return { rank: 'B', ...RANK_COLORS.B, description: 'Good financial stability', isGood: true };
  if (ratio <= 1.5) return { rank: 'C', ...RANK_COLORS.C, description: 'Moderate debt levels', isGood: false };
  if (ratio <= 2.5) return { rank: 'D', ...RANK_COLORS.D, description: 'High debt levels', isGood: false };
  return { rank: 'E', ...RANK_COLORS.E, description: 'Very high debt levels', isGood: false };
}

// Earnings Growth ranking
function getEarningsGrowthRanking(growth: number): MetricRanking {
  if (growth >= 25) return { rank: 'S', ...RANK_COLORS.S, description: 'Exceptional growth', isGood: true };
  if (growth >= 15) return { rank: 'A', ...RANK_COLORS.A, description: 'Excellent growth', isGood: true };
  if (growth >= 8) return { rank: 'B', ...RANK_COLORS.B, description: 'Good growth', isGood: true };
  if (growth >= 3) return { rank: 'C', ...RANK_COLORS.C, description: 'Moderate growth', isGood: true };
  if (growth >= 0) return { rank: 'D', ...RANK_COLORS.D, description: 'Slow growth', isGood: false };
  return { rank: 'E', ...RANK_COLORS.E, description: 'Declining earnings', isGood: false };
}

// Sales Growth ranking
function getSalesGrowthRanking(growth: number): MetricRanking {
  if (growth >= 20) return { rank: 'S', ...RANK_COLORS.S, description: 'Exceptional revenue growth', isGood: true };
  if (growth >= 12) return { rank: 'A', ...RANK_COLORS.A, description: 'Excellent revenue growth', isGood: true };
  if (growth >= 6) return { rank: 'B', ...RANK_COLORS.B, description: 'Good revenue growth', isGood: true };
  if (growth >= 2) return { rank: 'C', ...RANK_COLORS.C, description: 'Moderate revenue growth', isGood: true };
  if (growth >= 0) return { rank: 'D', ...RANK_COLORS.D, description: 'Slow revenue growth', isGood: false };
  return { rank: 'E', ...RANK_COLORS.E, description: 'Declining revenue', isGood: false };
}

// PEG Ratio ranking (lower is better, around 1.0 is ideal)
function getPegRatioRanking(peg: number): MetricRanking {
  if (peg <= 0.5) return { rank: 'S', ...RANK_COLORS.S, description: 'Excellent value for growth', isGood: true };
  if (peg <= 1.0) return { rank: 'A', ...RANK_COLORS.A, description: 'Good value for growth', isGood: true };
  if (peg <= 1.5) return { rank: 'B', ...RANK_COLORS.B, description: 'Fair value for growth', isGood: true };
  if (peg <= 2.0) return { rank: 'C', ...RANK_COLORS.C, description: 'Moderately expensive', isGood: false };
  if (peg <= 3.0) return { rank: 'D', ...RANK_COLORS.D, description: 'Expensive for growth', isGood: false };
  return { rank: 'E', ...RANK_COLORS.E, description: 'Very expensive for growth', isGood: false };
}

// Current Ratio ranking
function getCurrentRatioRanking(ratio: number): MetricRanking {
  if (ratio >= 2.5) return { rank: 'S', ...RANK_COLORS.S, description: 'Excellent liquidity', isGood: true };
  if (ratio >= 2.0) return { rank: 'A', ...RANK_COLORS.A, description: 'Very good liquidity', isGood: true };
  if (ratio >= 1.5) return { rank: 'B', ...RANK_COLORS.B, description: 'Good liquidity', isGood: true };
  if (ratio >= 1.0) return { rank: 'C', ...RANK_COLORS.C, description: 'Adequate liquidity', isGood: true };
  if (ratio >= 0.8) return { rank: 'D', ...RANK_COLORS.D, description: 'Poor liquidity', isGood: false };
  return { rank: 'E', ...RANK_COLORS.E, description: 'Very poor liquidity', isGood: false };
}

// Profit Margins ranking
function getProfitMarginRanking(margin: number): MetricRanking {
  if (margin >= 20) return { rank: 'S', ...RANK_COLORS.S, description: 'Exceptional profitability', isGood: true };
  if (margin >= 15) return { rank: 'A', ...RANK_COLORS.A, description: 'Excellent profitability', isGood: true };
  if (margin >= 10) return { rank: 'B', ...RANK_COLORS.B, description: 'Good profitability', isGood: true };
  if (margin >= 5) return { rank: 'C', ...RANK_COLORS.C, description: 'Average profitability', isGood: true };
  if (margin >= 0) return { rank: 'D', ...RANK_COLORS.D, description: 'Low profitability', isGood: false };
  return { rank: 'E', ...RANK_COLORS.E, description: 'Unprofitable', isGood: false };
}

// Metric definitions
export const METRIC_DEFINITIONS: Record<string, MetricInfo> = {
  returnOnEquity: {
    name: 'Return on Equity (ROE)',
    explanation: 'Measures how effectively a company uses shareholders\' equity to generate profits. Higher values indicate better management efficiency and profitability. A ROE above 15% is generally considered excellent.',
    unit: '%',
    higherIsBetter: true,
    getRanking: getRoeRanking
  },
  debtToEquity: {
    name: 'Debt-to-Equity Ratio',
    explanation: 'Compares total debt to shareholders\' equity, indicating financial leverage. Lower ratios suggest better financial stability and less risk. A ratio below 0.5 is generally considered very safe.',
    unit: 'ratio',
    higherIsBetter: false,
    getRanking: getDebtToEquityRanking
  },
  earningsGrowth: {
    name: 'Earnings Growth',
    explanation: 'Year-over-year percentage change in earnings per share. Higher growth rates indicate a company\'s ability to increase profitability over time. Growth above 15% is considered excellent.',
    unit: '%',
    higherIsBetter: true,
    getRanking: getEarningsGrowthRanking
  },
  salesGrowth: {
    name: 'Sales Growth',
    explanation: 'Year-over-year percentage change in total revenue. Consistent sales growth indicates market demand and business expansion. Growth above 10% is generally considered strong.',
    unit: '%',
    higherIsBetter: true,
    getRanking: getSalesGrowthRanking
  },
  pegRatio: {
    name: 'PEG Ratio',
    explanation: 'Price/Earnings ratio divided by earnings growth rate. Values around 1.0 suggest fair valuation relative to growth. Lower values indicate better value for growth investors.',
    unit: 'ratio',
    higherIsBetter: false,
    getRanking: getPegRatioRanking
  },
  currentRatio: {
    name: 'Current Ratio',
    explanation: 'Current assets divided by current liabilities. Measures a company\'s ability to pay short-term obligations. A ratio above 1.5 indicates good liquidity.',
    unit: 'ratio',
    higherIsBetter: true,
    getRanking: getCurrentRatioRanking
  },
  profitMargins: {
    name: 'Profit Margin',
    explanation: 'Net income as a percentage of revenue. Higher margins indicate better cost control and pricing power. Margins above 10% are generally considered good.',
    unit: '%',
    higherIsBetter: true,
    getRanking: getProfitMarginRanking
  }
};

// Helper function to get metric ranking
export function getMetricRanking(metricKey: string, value: number): MetricRanking | null {
  const metric = METRIC_DEFINITIONS[metricKey];
  if (!metric || value === null || value === undefined || isNaN(value)) {
    return null;
  }
  return metric.getRanking(value);
}

// Helper function to format metric value
export function formatMetricValue(metricKey: string, value: number): string {
  const metric = METRIC_DEFINITIONS[metricKey];
  if (!metric || value === null || value === undefined || isNaN(value)) {
    return 'N/A';
  }
  
  if (metric.unit === '%') {
    return `${value.toFixed(1)}%`;
  } else if (metric.unit === 'ratio') {
    return value.toFixed(2);
  }
  return value.toString();
}
