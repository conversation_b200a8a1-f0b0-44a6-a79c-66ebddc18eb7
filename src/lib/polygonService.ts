// Polygon.io API Service
// Comprehensive stock data service using Polygon.io REST API

const POLYGON_API_KEY = process.env.POLYGON_API_KEY || process.env.NEXT_PUBLIC_POLYGON_API_KEY;
const BASE_URL = 'https://api.polygon.io';

// Rate limiting: Disabled for paid subscription
const RATE_LIMIT_DELAY = 0; // No delay for paid subscription
let lastRequestTime = 0;

// Type definitions for Polygon.io API responses
export interface PolygonTickerSnapshot {
  ticker: string;
  todaysChangePerc: number;
  todaysChange: number;
  updated: number;
  timeframe: string;
  market_status: string;
  fmv?: number;
  day?: {
    c: number; // close
    h: number; // high
    l: number; // low
    o: number; // open
    v: number; // volume
    vw: number; // volume weighted average price
  };
  min?: {
    av: number; // accumulated volume
    c: number; // close
    h: number; // high
    l: number; // low
    o: number; // open
    t: number; // timestamp
    v: number; // volume
    vw: number; // volume weighted average price
  };
  prevDay?: {
    c: number;
    h: number;
    l: number;
    o: number;
    v: number;
    vw: number;
  };
  lastQuote?: {
    P: number; // bid price
    S: number; // bid size
    p: number; // ask price
    s: number; // ask size
    t: number; // timestamp
  };
  lastTrade?: {
    c: number[]; // conditions
    i: string; // trade ID
    p: number; // price
    s: number; // size
    t: number; // timestamp
    x: number; // exchange
  };
}

export interface PolygonTickerDetails {
  ticker: string;
  name: string;
  market: string;
  locale: string;
  primary_exchange: string;
  type: string;
  active: boolean;
  currency_name: string;
  cik?: string;
  composite_figi?: string;
  share_class_figi?: string;
  market_cap?: number;
  phone_number?: string;
  address?: {
    address1?: string;
    city?: string;
    state?: string;
    postal_code?: string;
  };
  description?: string;
  sic_code?: string;
  sic_description?: string;
  ticker_root?: string;
  homepage_url?: string;
  total_employees?: number;
  list_date?: string;
  branding?: {
    logo_url?: string;
    icon_url?: string;
  };
  share_class_shares_outstanding?: number;
  weighted_shares_outstanding?: number;
}

export interface PolygonAggregateBar {
  c: number; // close
  h: number; // high
  l: number; // low
  o: number; // open
  t: number; // timestamp
  v: number; // volume
  vw: number; // volume weighted average price
  n?: number; // number of transactions
}

export interface PolygonDividend {
  cash_amount: number;
  currency: string;
  declaration_date: string;
  dividend_type: string;
  ex_dividend_date: string;
  frequency: number;
  pay_date: string;
  record_date: string;
  ticker: string;
}

export interface PolygonTickerSearch {
  ticker: string;
  name: string;
  market: string;
  locale: string;
  primary_exchange: string;
  type: string;
  active: boolean;
  currency_name: string;
  last_updated_utc: string;
}

// Rate limiting helper (disabled for paid subscription)
async function rateLimitedRequest(url: string): Promise<Response> {
  // No rate limiting for paid subscription
  const response = await fetch(url);

  if (!response.ok) {
    if (response.status === 429) {
      console.log('Rate limit exceeded, waiting 60 seconds...');
      await new Promise(resolve => setTimeout(resolve, 60000));
      return rateLimitedRequest(url);
    }
    throw new Error(`Polygon API error: ${response.status} ${response.statusText}`);
  }

  return response;
}

export class PolygonService {
  private apiKey: string;

  constructor() {
    if (!POLYGON_API_KEY) {
      throw new Error('POLYGON_API_KEY environment variable is required');
    }
    this.apiKey = POLYGON_API_KEY;
  }

  // Get real-time snapshot for a single ticker
  async getTickerSnapshot(symbol: string): Promise<PolygonTickerSnapshot | null> {
    try {
      const url = `${BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}?apiKey=${this.apiKey}`;
      const response = await rateLimitedRequest(url);
      const data = await response.json();

      console.log(`Snapshot API response for ${symbol}:`, JSON.stringify(data, null, 2));

      if (data.status === 'OK' && data.results?.ticker) {
        return data.results.ticker;
      }

      // If snapshot fails, try to get data from previous day close
      console.warn(`Snapshot failed for ${symbol}, trying previous day data...`);
      const prevDayData = await this.getPreviousDayBar(symbol);
      if (prevDayData) {
        // Create a mock snapshot from previous day data
        return {
          ticker: symbol,
          todaysChangePerc: 0,
          todaysChange: 0,
          updated: Date.now(),
          timeframe: 'DELAYED',
          market_status: 'closed',
          day: {
            c: prevDayData.c,
            h: prevDayData.h,
            l: prevDayData.l,
            o: prevDayData.o,
            v: prevDayData.v,
            vw: prevDayData.vw
          }
        };
      }

      return null;
    } catch (error) {
      console.error(`Error fetching snapshot for ${symbol}:`, error);
      return null;
    }
  }

  // Get current price using real-time snapshot data (for paid subscription)
  async getStockPrice(symbol: string): Promise<{
    price: number;
    change: number;
    changePercent: number;
    marketCap?: number;
    volume?: number;
  } | null> {
    try {
      // Get real-time snapshot data
      const snapshot = await this.getTickerSnapshot(symbol);

      if (!snapshot) {
        console.warn(`No snapshot data available for ${symbol}`);
        return null;
      }

      // Use the most recent trade price or day close price
      let currentPrice = 0;
      let volume = 0;

      // Priority: lastTrade > day.close > min.close
      if (snapshot.lastTrade?.p) {
        currentPrice = snapshot.lastTrade.p;
        console.log(`Using last trade price for ${symbol}: $${currentPrice}`);
      } else if (snapshot.day?.c) {
        currentPrice = snapshot.day.c;
        volume = snapshot.day.v || 0;
        console.log(`Using day close price for ${symbol}: $${currentPrice}`);
      } else if (snapshot.min?.c) {
        currentPrice = snapshot.min.c;
        volume = snapshot.min.v || 0;
        console.log(`Using minute close price for ${symbol}: $${currentPrice}`);
      } else {
        console.warn(`No price data available in snapshot for ${symbol}`);
        return null;
      }

      // Get change data from snapshot
      const change = snapshot.todaysChange || 0;
      const changePercent = snapshot.todaysChangePerc || 0;

      // Try to get market cap from ticker details (optional)
      let marketCap: number | undefined;
      try {
        const details = await this.getTickerDetails(symbol);
        marketCap = details?.market_cap;
      } catch (detailsError) {
        console.warn(`Could not get market cap for ${symbol}:`, detailsError);
      }

      console.log(`Real-time price for ${symbol}: $${currentPrice.toFixed(2)} (${changePercent.toFixed(2)}%)`);

      return {
        price: currentPrice,
        change,
        changePercent,
        marketCap,
        volume
      };
    } catch (error) {
      console.error(`Error fetching real-time price for ${symbol}:`, error);
      return null;
    }
  }

  // Get real-time prices for multiple stocks efficiently
  async getBatchStockPrices(symbols: string[]): Promise<{
    [symbol: string]: {
      price: number;
      change: number;
      changePercent: number;
      volume?: number;
    } | null;
  }> {
    const results: { [symbol: string]: any } = {};

    // Process all symbols in parallel for maximum speed
    const promises = symbols.map(async (symbol) => {
      try {
        const priceData = await this.getStockPrice(symbol);
        return { symbol, data: priceData };
      } catch (error) {
        console.error(`Error fetching price for ${symbol}:`, error);
        return { symbol, data: null };
      }
    });

    const responses = await Promise.all(promises);

    responses.forEach(({ symbol, data }) => {
      results[symbol] = data;
    });

    return results;
  }

  // Get detailed company information
  async getTickerDetails(symbol: string): Promise<PolygonTickerDetails | null> {
    try {
      const url = `${BASE_URL}/v3/reference/tickers/${symbol}?apiKey=${this.apiKey}`;
      const response = await rateLimitedRequest(url);
      const data = await response.json();
      
      if (data.status === 'OK' && data.results) {
        return data.results;
      }
      
      return null;
    } catch (error) {
      console.error(`Error fetching details for ${symbol}:`, error);
      return null;
    }
  }

  // Get historical aggregate bars (OHLCV data)
  async getAggregates(
    symbol: string,
    multiplier: number = 1,
    timespan: 'minute' | 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year' = 'day',
    from: string,
    to: string
  ): Promise<PolygonAggregateBar[]> {
    try {
      const url = `${BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}?adjusted=true&sort=asc&apiKey=${this.apiKey}`;
      const response = await rateLimitedRequest(url);
      const data = await response.json();
      
      if (data.status === 'OK' && data.results) {
        return data.results;
      }
      
      return [];
    } catch (error) {
      console.error(`Error fetching aggregates for ${symbol}:`, error);
      return [];
    }
  }

  // Get previous day's bar
  async getPreviousDayBar(symbol: string): Promise<PolygonAggregateBar | null> {
    try {
      const url = `${BASE_URL}/v2/aggs/ticker/${symbol}/prev?adjusted=true&apiKey=${this.apiKey}`;
      const response = await rateLimitedRequest(url);
      const data = await response.json();
      
      if (data.status === 'OK' && data.results && data.results.length > 0) {
        return data.results[0];
      }
      
      return null;
    } catch (error) {
      console.error(`Error fetching previous day bar for ${symbol}:`, error);
      return null;
    }
  }

  // Search for tickers
  async searchTickers(query: string, limit: number = 10): Promise<PolygonTickerSearch[]> {
    try {
      const url = `${BASE_URL}/v3/reference/tickers?search=${encodeURIComponent(query)}&active=true&limit=${limit}&apiKey=${this.apiKey}`;
      const response = await rateLimitedRequest(url);
      const data = await response.json();
      
      if (data.status === 'OK' && data.results) {
        return data.results;
      }
      
      return [];
    } catch (error) {
      console.error(`Error searching tickers for "${query}":`, error);
      return [];
    }
  }

  // Get dividends for a ticker
  async getDividends(symbol: string, limit: number = 10): Promise<PolygonDividend[]> {
    try {
      const url = `${BASE_URL}/v3/reference/dividends?ticker=${symbol}&limit=${limit}&apiKey=${this.apiKey}`;
      const response = await rateLimitedRequest(url);
      const data = await response.json();
      
      if (data.status === 'OK' && data.results) {
        return data.results;
      }
      
      return [];
    } catch (error) {
      console.error(`Error fetching dividends for ${symbol}:`, error);
      return [];
    }
  }

  // Get market status
  async getMarketStatus(): Promise<any> {
    try {
      const url = `${BASE_URL}/v1/marketstatus/now?apiKey=${this.apiKey}`;
      const response = await rateLimitedRequest(url);
      const data = await response.json();
      
      return data;
    } catch (error) {
      console.error('Error fetching market status:', error);
      return null;
    }
  }

  // Get full market snapshot (top gainers/losers)
  async getMarketSnapshot(direction: 'gainers' | 'losers' = 'gainers'): Promise<PolygonTickerSnapshot[]> {
    try {
      const url = `${BASE_URL}/v2/snapshot/locale/us/markets/stocks/${direction}?apiKey=${this.apiKey}`;
      const response = await rateLimitedRequest(url);
      const data = await response.json();
      
      if (data.status === 'OK' && data.results) {
        return data.results;
      }
      
      return [];
    } catch (error) {
      console.error(`Error fetching market ${direction}:`, error);
      return [];
    }
  }
}

export const polygonService = new PolygonService();
