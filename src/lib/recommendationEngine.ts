import { StockData, YahooFinanceService } from './yahooFinance';
import { StockScoringEngine, StockScore } from './stockScoring';

export interface StockRecommendation {
  symbol: string;
  companyName: string;
  score: number;
  category: 'Lower Risk' | 'Balanced Risk' | 'Full Throttle';
  criteriaPassedCount: number;
  criteriaPassedPercentage: number;
  metrics: {
    earningsGrowth1Yr: number;
    salesGrowth1Yr: number;
    salesGrowth5Yr: number;
    debtToEquity: number;
    freeCashFlow: string;
    pegRatio: number;
    returnOnEquity: number;
  };
  recommendation: 'BUY' | 'HOLD' | 'SELL';
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  currentPrice: number;
  marketCap: string;
}

export interface RecommendationStrategy {
  name: string;
  description: string;
  criteria: Array<{
    metric: string;
    condition: string;
    weight: number;
  }>;
}

// Investment strategies similar to Hello Stocks
export const INVESTMENT_STRATEGIES: Record<string, RecommendationStrategy> = {
  'Lower Risk': {
    name: 'Lower Risk (Hello Stocks)',
    description: 'Strong financials, solid growth and trading at a low price.',
    criteria: [
      { metric: 'P/E Ratio', condition: '< 25', weight: 1 },
      { metric: 'Debt-to-Equity', condition: '< 0.8', weight: 1 },
      { metric: 'ROE', condition: '> 15%', weight: 1 },
      { metric: 'Revenue Growth', condition: '> 5%', weight: 1 },
      { metric: 'Free Cash Flow', condition: 'Positive', weight: 1 },
      { metric: 'Current Ratio', condition: '> 1.2', weight: 1 },
      { metric: 'Profit Margins', condition: '> 10%', weight: 1 }
    ]
  },
  'Balanced Risk': {
    name: 'Balanced Risk (Hello Stocks)',
    description: 'Strong financials, solid growth and trading at a fair to low price.',
    criteria: [
      { metric: 'P/E Ratio', condition: '< 35', weight: 1 },
      { metric: 'Revenue Growth', condition: '> 8%', weight: 1 },
      { metric: 'Earnings Growth 5Yr', condition: '> 15%', weight: 1 },
      { metric: 'Sales Growth 5Yr', condition: '> 20%', weight: 1 },
      { metric: 'Debt-to-Equity', condition: '< 2.0', weight: 1 },
      { metric: 'Free Cash Flow', condition: 'Positive', weight: 1 },
      { metric: 'PEG Ratio', condition: '< 3.0', weight: 1 }
    ]
  },
  'Full Throttle': {
    name: 'Full Throttle (Hello Stocks)',
    description: 'Massive growth which are trading at a fair price.',
    criteria: [
      { metric: 'Revenue Growth 1Yr', condition: '> 20%', weight: 1 },
      { metric: 'Sales Growth 5Yr', condition: '> 100%', weight: 1 },
      { metric: 'Debt-to-Equity', condition: '< 3.0', weight: 1 },
      { metric: 'PEG Ratio', condition: '< 2.0', weight: 1 }
    ]
  }
};

// Popular stocks to analyze (similar to Hello Stocks featured stocks)
export const FEATURED_STOCKS = [
  'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA', 'V', 'JPM', 'JNJ',
  'WMT', 'PG', 'UNH', 'HD', 'MA', 'DIS', 'PYPL', 'ADBE', 'NFLX', 'CRM',
  'NVDA', 'AMD', 'INTC', 'ORCL', 'CSCO', 'IBM', 'QCOM', 'TXN', 'AVGO', 'MU',
  'NEM', 'PINS', 'INCY', 'PLTR', 'NOW', 'INTU', 'PGR', 'APH', 'LRCX', 'PANW'
];

export class RecommendationEngine {
  private static formatCurrency(value: number): string {
    if (value >= 1e12) return `$${(value / 1e12).toFixed(2)} Trillion`;
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)} Billion`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)} Million`;
    if (value >= 1e3) return `$${(value / 1e3).toFixed(2)} Thousand`;
    return `$${value.toFixed(2)}`;
  }

  private static async getStockData(symbol: string): Promise<StockData | null> {
    try {
      return await YahooFinanceService.getStockData(symbol);
    } catch (error) {
      console.error(`Failed to fetch data for ${symbol}:`, error);
      return null; // Return null instead of throwing error
    }
  }

  static async analyzeStock(symbol: string): Promise<StockRecommendation | null> {
    const stockData = await this.getStockData(symbol);
    if (!stockData) return null;

    const stockScore = StockScoringEngine.calculateScore(stockData);
    const criteriaPassedCount = StockScoringEngine.getCriteriaPassedCount(stockData);
    const criteriaPassedPercentage = StockScoringEngine.getCriteriaPassedPercentage(stockData);

    return {
      symbol: stockData.quote.symbol,
      companyName: stockData.quote.shortName || stockData.quote.longName,
      score: stockScore.overallScore,
      category: stockScore.category,
      criteriaPassedCount,
      criteriaPassedPercentage,
      metrics: {
        earningsGrowth1Yr: stockData.financials.earningsGrowth * 100,
        salesGrowth1Yr: stockData.financials.revenueGrowth * 100,
        salesGrowth5Yr: stockData.financials.revenueGrowth * 100 * 1.5, // Approximation
        debtToEquity: stockData.financials.debtToEquity,
        freeCashFlow: this.formatCurrency(stockData.financials.freeCashflow),
        pegRatio: stockData.keyStats.pegRatio || 0,
        returnOnEquity: stockData.financials.returnOnEquity * 100
      },
      recommendation: stockScore.recommendation,
      riskLevel: stockScore.riskLevel,
      currentPrice: stockData.quote.regularMarketPrice,
      marketCap: this.formatCurrency(stockData.quote.marketCap)
    };
  }

  static async getRecommendationsByCategory(
    category: 'Lower Risk' | 'Balanced Risk' | 'Full Throttle',
    limit: number = 20
  ): Promise<StockRecommendation[]> {
    const recommendations: StockRecommendation[] = [];
    
    // Analyze featured stocks
    for (const symbol of FEATURED_STOCKS.slice(0, limit * 2)) {
      try {
        const recommendation = await this.analyzeStock(symbol);
        if (recommendation && recommendation.category === category) {
          recommendations.push(recommendation);
        }
      } catch (error) {
        console.error(`Error analyzing ${symbol}:`, error);
      }
    }

    // Sort by score (highest first) and limit results
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  static async getAllRecommendations(): Promise<{
    lowerRisk: StockRecommendation[];
    balancedRisk: StockRecommendation[];
    fullThrottle: StockRecommendation[];
  }> {
    const [lowerRisk, balancedRisk, fullThrottle] = await Promise.all([
      this.getRecommendationsByCategory('Lower Risk', 15),
      this.getRecommendationsByCategory('Balanced Risk', 15),
      this.getRecommendationsByCategory('Full Throttle', 15)
    ]);

    return {
      lowerRisk,
      balancedRisk,
      fullThrottle
    };
  }

  static async searchAndAnalyze(query: string): Promise<StockRecommendation[]> {
    try {
      // Search for stocks matching the query
      const searchResults = await YahooFinanceService.searchStocks(query);
      const recommendations: StockRecommendation[] = [];

      // Analyze the first few search results
      for (const result of searchResults.slice(0, 5)) {
        const recommendation = await this.analyzeStock(result.symbol);
        if (recommendation) {
          recommendations.push(recommendation);
        }
      }

      return recommendations.sort((a, b) => b.score - a.score);
    } catch (error) {
      console.error('Error in search and analyze:', error);
      return [];
    }
  }

  // Generate real-time recommendations based on popular stocks
  static async generateRecommendations(): Promise<{
    lowerRisk: StockRecommendation[];
    balancedRisk: StockRecommendation[];
    fullThrottle: StockRecommendation[];
  }> {
    // Popular stocks to analyze for recommendations
    const popularSymbols = [
      'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA', 'V', 'JPM', 'JNJ',
      'PG', 'UNH', 'HD', 'MA', 'DIS', 'NFLX', 'CRM', 'ADBE', 'PYPL', 'INTC'
    ];

    const recommendations: StockRecommendation[] = [];

    // Analyze each stock
    for (const symbol of popularSymbols) {
      try {
        const analysis = await this.analyzeStock(symbol);
        if (analysis) {
          recommendations.push(analysis);
        }
      } catch (error) {
        console.warn(`Failed to analyze ${symbol}:`, error);
        // Continue with other stocks
      }
    }


    return {
      lowerRisk: recommendations.filter(r => r.category === 'Lower Risk'),
      balancedRisk: recommendations.filter(r => r.category === 'Balanced Risk'),
      fullThrottle: recommendations.filter(r => r.category === 'Full Throttle')
    };
  }
}

