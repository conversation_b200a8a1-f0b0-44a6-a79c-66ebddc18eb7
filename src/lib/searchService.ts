import { YahooFinanceService } from './yahooFinance';
import { RecommendationEngine, StockRecommendation } from './recommendationEngine';

// Stock popularity tracking (in-memory for demo, use database in production)
const stockPopularity: Record<string, number> = {};

export interface SearchResult {
  symbol: string;
  name: string;
  type: 'stock' | 'etf' | 'index';
  exchange?: string;
}

export interface DetailedSearchResult extends SearchResult {
  analysis?: StockRecommendation;
  price?: number;
  change?: number;
  changePercent?: number;
}

export class SearchService {
  private static cache = new Map<string, DetailedSearchResult[]>();
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes



  static async searchStocks(query: string): Promise<DetailedSearchResult[]> {
    if (!query || query.trim().length < 1) {
      return [];
    }

    const normalizedQuery = query.trim().toLowerCase();
    const cacheKey = `search_${normalizedQuery}`;

    // Check cache first
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!;
      return cached;
    }

    try {
      // First, search in local database
      const localResults = await YahooFinanceService.searchStocks(query);
      let results: SearchResult[] = localResults.map(result => ({
        symbol: result.symbol,
        name: result.name,
        type: 'stock' as const,
        exchange: 'NASDAQ' // Default exchange, could be enhanced
      }));

      // Note: Polygon API search is now handled in the main search API route

      // Limit to top 10 results
      results = results.slice(0, 10);

      // Convert to detailed results and add analysis for top results
      const detailedResults: DetailedSearchResult[] = await Promise.all(
        results.map(async (result, index) => {
          const detailed: DetailedSearchResult = { ...result };

          // Get price data for all results, but only analyze the first 3 to avoid too many API calls
          try {
            // Get basic quote data for price display
            const quote = await YahooFinanceService.getStockQuote(result.symbol);
            if (quote) {
              detailed.price = quote.regularMarketPrice;
              detailed.change = quote.regularMarketChange;
              detailed.changePercent = quote.regularMarketChangePercent;
            }

            // Only do full analysis for first 3 results
            if (index < 3) {
              const analysis = await RecommendationEngine.analyzeStock(result.symbol);
              if (analysis) {
                detailed.analysis = analysis;
                detailed.price = analysis.currentPrice; // Use analysis price if available
              }
            }
          } catch (error) {
            console.warn(`Failed to get data for ${result.symbol}:`, error);
          }

          return detailed;
        })
      );

      // Cache the results
      this.cache.set(cacheKey, detailedResults);

      // Clear cache after duration
      setTimeout(() => {
        this.cache.delete(cacheKey);
      }, this.CACHE_DURATION);

      return detailedResults;

    } catch (error) {
      console.error('Search error:', error);
      return [];
    }
  }

  static async getStockAnalysis(symbol: string): Promise<StockRecommendation | null> {
    try {
      return await RecommendationEngine.analyzeStock(symbol.toUpperCase());
    } catch (error) {
      console.error(`Error analyzing ${symbol}:`, error);
      return null;
    }
  }

  static getPopularStocksList(): SearchResult[] {
    // Return empty array since we're using real API data now
    return [];
  }

  static getSuggestions(query: string): SearchResult[] {
    if (!query || query.trim().length < 1) {
      return this.getPopularStocksList();
    }

    // For real-time suggestions, we would need to implement a proper search API
    // For now, return empty array to force real API calls
    return [];
  }

  // Clear all cached search results
  static clearCache(): void {
    this.cache.clear();
  }

  // Get cache statistics
  static getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  // Get trending stocks (most searched recently)
  static async getTrendingStocks(): Promise<StockRecommendation[]> {
    try {
      // Use most popular symbols as trending (in real app, this would come from analytics)
      const trendingSymbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META'];

      // Get analysis for trending stocks
      const trendingStocks = await Promise.all(
        trendingSymbols.map(async (symbol) => {
          try {
            return await RecommendationEngine.analyzeStock(symbol);
          } catch (error) {
            console.warn(`Failed to analyze trending stock ${symbol}:`, error);
            return null;
          }
        })
      );

      return trendingStocks.filter((stock): stock is StockRecommendation => stock !== null);
    } catch (error) {
      console.error('Error getting trending stocks:', error);
      return [];
    }
  }

  // Get popular stocks from database based on actual user activity
  static async getPopularStocks(): Promise<StockRecommendation[]> {
    try {
      // Get popular stocks from database (based on actual data)
      const response = await fetch('/api/stocks/popular');
      if (!response.ok) {
        throw new Error('Failed to fetch popular stocks');
      }

      const data = await response.json();
      const popularSymbols = data.symbols || [];

      if (popularSymbols.length === 0) {
        console.warn('No popular stocks found in database');
        return [];
      }

      // Get analysis for popular stocks
      const popularStocks = await Promise.all(
        popularSymbols.map(async (symbol: string) => {
          try {
            return await RecommendationEngine.analyzeStock(symbol);
          } catch (error) {
            console.warn(`Failed to analyze popular stock ${symbol}:`, error);
            return null;
          }
        })
      );

      return popularStocks.filter((stock): stock is StockRecommendation => stock !== null);
    } catch (error) {
      console.error('Error getting popular stocks:', error);
      return [];
    }
  }

  // Increment stock popularity when clicked/searched
  static async incrementStockPopularity(symbol: string): Promise<void> {
    try {
      const upperSymbol = symbol.toUpperCase();
      if (stockPopularity[upperSymbol]) {
        stockPopularity[upperSymbol] += 1;
      } else {
        stockPopularity[upperSymbol] = 1;
      }

      // In production, this would save to database
      console.log(`Incremented popularity for ${upperSymbol}: ${stockPopularity[upperSymbol]}`);
    } catch (error) {
      console.error('Error incrementing stock popularity:', error);
    }
  }

  // Get stock popularity score
  static getStockPopularity(symbol: string): number {
    return stockPopularity[symbol.toUpperCase()] || 0;
  }

  // Get top stocks by popularity
  static getTopStocksByPopularity(limit: number = 10): Array<{ symbol: string; popularity: number }> {
    return Object.entries(stockPopularity)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([symbol, popularity]) => ({ symbol, popularity }));
  }
}
