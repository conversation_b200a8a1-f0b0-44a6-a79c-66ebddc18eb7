import { StockData } from './yahooFinance';

export interface ScoringCriteria {
  name: string;
  weight: number;
  calculate: (data: StockData) => number;
  description: string;
}

export interface StockScore {
  overallScore: number;
  criteriaScores: Array<{
    name: string;
    score: number;
    weight: number;
    description: string;
  }>;
  recommendation: 'BUY' | 'HOLD' | 'SELL';
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  category: 'Lower Risk' | 'Balanced Risk' | 'Full Throttle';
}

// Scoring criteria based on the Hello Stocks methodology
export const SCORING_CRITERIA: ScoringCriteria[] = [
  {
    name: 'P/E Ratio',
    weight: 0.15,
    calculate: (data: StockData) => {
      const pe = data.quote.trailingPE;
      if (!pe || pe <= 0) return 5;
      
      // Lower P/E is better (more value)
      if (pe < 10) return 10;
      if (pe < 15) return 9;
      if (pe < 20) return 8;
      if (pe < 25) return 7;
      if (pe < 30) return 6;
      if (pe < 40) return 5;
      if (pe < 50) return 4;
      if (pe < 60) return 3;
      if (pe < 80) return 2;
      return 1;
    },
    description: 'Price-to-Earnings ratio indicates valuation'
  },
  {
    name: 'PEG Ratio',
    weight: 0.15,
    calculate: (data: StockData) => {
      const peg = data.keyStats.pegRatio;
      if (!peg || peg <= 0) return 5;
      
      // PEG < 1 is excellent, 1-2 is good
      if (peg < 0.5) return 10;
      if (peg < 1.0) return 9;
      if (peg < 1.5) return 8;
      if (peg < 2.0) return 7;
      if (peg < 2.5) return 6;
      if (peg < 3.0) return 5;
      if (peg < 4.0) return 4;
      if (peg < 5.0) return 3;
      if (peg < 7.0) return 2;
      return 1;
    },
    description: 'PEG ratio considers growth in valuation'
  },
  {
    name: 'Debt-to-Equity',
    weight: 0.12,
    calculate: (data: StockData) => {
      const debtToEquity = data.financials.debtToEquity;
      if (debtToEquity < 0) return 5;
      
      // Lower debt-to-equity is better
      if (debtToEquity < 0.1) return 10;
      if (debtToEquity < 0.3) return 9;
      if (debtToEquity < 0.5) return 8;
      if (debtToEquity < 0.7) return 7;
      if (debtToEquity < 1.0) return 6;
      if (debtToEquity < 1.5) return 5;
      if (debtToEquity < 2.0) return 4;
      if (debtToEquity < 3.0) return 3;
      if (debtToEquity < 4.0) return 2;
      return 1;
    },
    description: 'Lower debt relative to equity is safer'
  },
  {
    name: 'Return on Equity',
    weight: 0.12,
    calculate: (data: StockData) => {
      const roe = data.financials.returnOnEquity * 100; // Convert to percentage
      if (roe < 0) return 1;
      
      // Higher ROE is better
      if (roe > 50) return 10;
      if (roe > 30) return 9;
      if (roe > 20) return 8;
      if (roe > 15) return 7;
      if (roe > 12) return 6;
      if (roe > 10) return 5;
      if (roe > 8) return 4;
      if (roe > 5) return 3;
      if (roe > 2) return 2;
      return 1;
    },
    description: 'Return on Equity measures profitability efficiency'
  },
  {
    name: 'Revenue Growth',
    weight: 0.12,
    calculate: (data: StockData) => {
      const growth = data.financials.revenueGrowth * 100; // Convert to percentage
      if (growth < 0) return 1;
      
      // Higher growth is better
      if (growth > 50) return 10;
      if (growth > 30) return 9;
      if (growth > 20) return 8;
      if (growth > 15) return 7;
      if (growth > 10) return 6;
      if (growth > 7) return 5;
      if (growth > 5) return 4;
      if (growth > 3) return 3;
      if (growth > 1) return 2;
      return 1;
    },
    description: 'Revenue growth indicates business expansion'
  },
  {
    name: 'Earnings Growth',
    weight: 0.12,
    calculate: (data: StockData) => {
      const growth = data.financials.earningsGrowth * 100; // Convert to percentage
      if (growth < 0) return 1;
      
      // Higher earnings growth is better
      if (growth > 100) return 10;
      if (growth > 50) return 9;
      if (growth > 30) return 8;
      if (growth > 20) return 7;
      if (growth > 15) return 6;
      if (growth > 10) return 5;
      if (growth > 7) return 4;
      if (growth > 5) return 3;
      if (growth > 2) return 2;
      return 1;
    },
    description: 'Earnings growth shows profit expansion'
  },
  {
    name: 'Free Cash Flow',
    weight: 0.10,
    calculate: (data: StockData) => {
      const fcf = data.financials.freeCashflow;
      const marketCap = data.quote.marketCap;
      
      if (!fcf || !marketCap || fcf <= 0) return 1;
      
      const fcfYield = (fcf / marketCap) * 100; // FCF yield as percentage
      
      // Higher FCF yield is better
      if (fcfYield > 15) return 10;
      if (fcfYield > 10) return 9;
      if (fcfYield > 8) return 8;
      if (fcfYield > 6) return 7;
      if (fcfYield > 4) return 6;
      if (fcfYield > 3) return 5;
      if (fcfYield > 2) return 4;
      if (fcfYield > 1) return 3;
      if (fcfYield > 0.5) return 2;
      return 1;
    },
    description: 'Free cash flow relative to market cap'
  },
  {
    name: 'Profit Margins',
    weight: 0.08,
    calculate: (data: StockData) => {
      const margin = data.financials.profitMargins * 100; // Convert to percentage
      if (margin < 0) return 1;
      
      // Higher profit margins are better
      if (margin > 30) return 10;
      if (margin > 25) return 9;
      if (margin > 20) return 8;
      if (margin > 15) return 7;
      if (margin > 12) return 6;
      if (margin > 10) return 5;
      if (margin > 8) return 4;
      if (margin > 5) return 3;
      if (margin > 2) return 2;
      return 1;
    },
    description: 'Profit margins indicate operational efficiency'
  },
  {
    name: 'Current Ratio',
    weight: 0.04,
    calculate: (data: StockData) => {
      const ratio = data.financials.currentRatio;
      if (ratio <= 0) return 1;
      
      // Optimal current ratio is around 1.5-3
      if (ratio >= 1.5 && ratio <= 3) return 10;
      if (ratio >= 1.2 && ratio < 1.5) return 8;
      if (ratio >= 1.0 && ratio < 1.2) return 6;
      if (ratio >= 0.8 && ratio < 1.0) return 4;
      if (ratio > 3 && ratio <= 4) return 7;
      if (ratio > 4) return 5;
      return 2;
    },
    description: 'Current ratio measures short-term liquidity'
  }
];

export class StockScoringEngine {
  static calculateScore(stockData: StockData): StockScore {
    const criteriaScores = SCORING_CRITERIA.map(criteria => {
      const score = criteria.calculate(stockData);
      return {
        name: criteria.name,
        score: Math.max(1, Math.min(10, score)), // Ensure score is between 1-10
        weight: criteria.weight,
        description: criteria.description
      };
    });

    // Calculate weighted average
    const totalWeightedScore = criteriaScores.reduce(
      (sum, item) => sum + (item.score * item.weight), 
      0
    );
    const totalWeight = criteriaScores.reduce((sum, item) => sum + item.weight, 0);
    const overallScore = Math.round((totalWeightedScore / totalWeight) * 10) / 10;

    // Determine recommendation
    let recommendation: 'BUY' | 'HOLD' | 'SELL';
    if (overallScore >= 8) recommendation = 'BUY';
    else if (overallScore >= 6) recommendation = 'HOLD';
    else recommendation = 'SELL';

    // Determine risk level based on volatility and debt
    const debtToEquity = stockData.financials.debtToEquity;
    const beta = stockData.quote.beta || 1;
    
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
    if (debtToEquity < 0.5 && beta < 1.2) riskLevel = 'LOW';
    else if (debtToEquity < 1.5 && beta < 1.8) riskLevel = 'MEDIUM';
    else riskLevel = 'HIGH';

    // Determine category based on growth and risk
    const revenueGrowth = stockData.financials.revenueGrowth * 100;
    const earningsGrowth = stockData.financials.earningsGrowth * 100;
    
    let category: 'Lower Risk' | 'Balanced Risk' | 'Full Throttle';
    if (riskLevel === 'LOW' && (revenueGrowth < 15 || earningsGrowth < 20)) {
      category = 'Lower Risk';
    } else if (revenueGrowth > 30 || earningsGrowth > 50) {
      category = 'Full Throttle';
    } else {
      category = 'Balanced Risk';
    }

    return {
      overallScore,
      criteriaScores,
      recommendation,
      riskLevel,
      category
    };
  }

  static getCriteriaPassedCount(stockData: StockData, minScore: number = 7): number {
    const scores = SCORING_CRITERIA.map(criteria => criteria.calculate(stockData));
    return scores.filter(score => score >= minScore).length;
  }

  static getCriteriaPassedPercentage(stockData: StockData, minScore: number = 7): number {
    const passedCount = this.getCriteriaPassedCount(stockData, minScore);
    return Math.round((passedCount / SCORING_CRITERIA.length) * 100);
  }
}
