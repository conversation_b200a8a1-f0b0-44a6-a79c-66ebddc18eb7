import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.');
}

export const supabase = createClient(supabaseUrl, supabaseKey);

// Database types
export interface Stock {
  id: string;
  symbol: string;
  name: string;
  exchange?: string;
  sector?: string;
  industry?: string;
  market_cap?: number; // Now stored as NUMERIC in DB but still number in TypeScript
  created_at: string;
  updated_at: string;
}

export interface StockQuote {
  id: string;
  symbol: string;
  price: number;
  change_amount?: number;
  change_percent?: number;
  volume?: number; // Now stored as NUMERIC in DB but still number in TypeScript
  market_cap?: number; // Now stored as NUMERIC in DB but still number in TypeScript
  pe_ratio?: number;
  forward_pe?: number;
  price_to_book?: number;
  dividend_yield?: number;
  beta?: number;
  week_52_low?: number;
  week_52_high?: number;
  currency: string;
  scraped_at: string;
}

export interface StockFinancials {
  id: string;
  symbol: string;
  total_revenue?: number; // Now stored as NUMERIC in DB but still number in TypeScript
  total_debt?: number; // Now stored as NUMERIC in DB but still number in TypeScript
  total_cash?: number; // Now stored as NUMERIC in DB but still number in TypeScript
  free_cashflow?: number; // Now stored as NUMERIC in DB but still number in TypeScript
  operating_cashflow?: number; // Now stored as NUMERIC in DB but still number in TypeScript
  revenue_growth?: number;
  earnings_growth?: number;
  gross_margins?: number;
  operating_margins?: number;
  profit_margins?: number;
  return_on_assets?: number;
  return_on_equity?: number;
  debt_to_equity?: number;
  current_ratio?: number;
  quick_ratio?: number;
  peg_ratio?: number;
  enterprise_value?: number; // Now stored as NUMERIC in DB but still number in TypeScript
  book_value?: number;
  shares_outstanding?: number; // Now stored as NUMERIC in DB but still number in TypeScript
  scraped_at: string;
}

export interface StockAnalysis {
  id: string;
  symbol: string;
  overall_score: number;
  recommendation: 'BUY' | 'HOLD' | 'SELL';
  risk_level: 'LOW' | 'MEDIUM' | 'HIGH';
  category: 'Lower Risk' | 'Balanced Risk' | 'Full Throttle';
  criteria_passed_count: number;
  criteria_passed_percentage: number;
  analysis_data?: any;
  calculated_at: string;
}

export interface SearchHistory {
  id: string;
  symbol: string;
  search_count: number;
  last_searched: string;
}

export interface ScrapingLog {
  id: string;
  symbol?: string;
  scrape_type: string;
  status: 'success' | 'error' | 'partial';
  error_message?: string;
  records_updated: number;
  scraped_at: string;
}

// Database service class
export class DatabaseService {
  // Get stock by symbol
  static async getStock(symbol: string): Promise<Stock | null> {
    const { data, error } = await supabase
      .from('stocks')
      .select('*')
      .eq('symbol', symbol.toUpperCase())
      .single();

    if (error) {
      console.error('Error fetching stock:', error);
      return null;
    }

    return data;
  }

  // Get latest quote for a stock
  static async getLatestQuote(symbol: string): Promise<StockQuote | null> {
    const { data, error } = await supabase
      .from('stock_quotes')
      .select('*')
      .eq('symbol', symbol.toUpperCase())
      .order('scraped_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      // Only log if it's not a "no rows" error (which is expected for missing stocks)
      if (error.code !== 'PGRST116') {
        console.error(`Error fetching quote for ${symbol}:`, error);
      }
      return null;
    }

    return data;
  }

  // Get latest financials for a stock
  static async getLatestFinancials(symbol: string): Promise<StockFinancials | null> {
    const { data, error } = await supabase
      .from('stock_financials')
      .select('*')
      .eq('symbol', symbol.toUpperCase())
      .order('scraped_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      // Only log if it's not a "no rows" error (which is expected for missing stocks)
      if (error.code !== 'PGRST116') {
        console.error(`Error fetching financials for ${symbol}:`, error);
      }
      return null;
    }

    return data;
  }

  // Get latest analysis for a stock
  static async getLatestAnalysis(symbol: string): Promise<StockAnalysis | null> {
    const { data, error } = await supabase
      .from('stock_analysis')
      .select('*')
      .eq('symbol', symbol.toUpperCase())
      .order('calculated_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      // Only log if it's not a "no rows" error (which is expected for missing stocks)
      if (error.code !== 'PGRST116') {
        console.error(`Error fetching analysis for ${symbol}:`, error);
      }
      return null;
    }

    return data;
  }

  // Search stocks by symbol or name
  static async searchStocks(query: string): Promise<Stock[]> {
    const { data, error } = await supabase
      .from('stocks')
      .select('*')
      .or(`symbol.ilike.%${query}%,name.ilike.%${query}%`)
      .limit(10);

    if (error) {
      console.error('Error searching stocks:', error);
      return [];
    }

    return data || [];
  }

  // Add a new stock to the database
  static async addStock(stockData: {
    symbol: string;
    name: string;
    exchange?: string;
    sector?: string;
    industry?: string;
  }): Promise<Stock | null> {
    try {
      // Check if stock already exists using a simpler query
      const { data: existingStock, error: checkError } = await supabase
        .from('stocks')
        .select('*')
        .eq('symbol', stockData.symbol.toUpperCase())
        .maybeSingle();

      if (checkError && checkError.code !== 'PGRST116') {
        console.error('Error checking existing stock:', checkError);
        return null;
      }

      if (existingStock) {
        console.log(`Stock ${stockData.symbol} already exists in database`);
        return existingStock;
      }

      // Insert new stock
      const { data, error } = await supabase
        .from('stocks')
        .insert({
          symbol: stockData.symbol.toUpperCase(),
          name: stockData.name,
          exchange: stockData.exchange || 'UNKNOWN',
          sector: stockData.sector || 'Unknown',
          industry: stockData.industry || 'Unknown'
        })
        .select()
        .single();

      if (error) {
        console.error('Error adding stock:', error);
        return null;
      }

      console.log(`Successfully added stock ${stockData.symbol} to database`);
      return data;
    } catch (error) {
      console.error('Error in addStock:', error);
      return null;
    }
  }

  // Add a new quote to the database
  static async addQuote(quoteData: {
    symbol: string;
    price: number;
    change_amount?: number;
    change_percent?: number;
    volume?: number;
    market_cap?: number;
    currency?: string;
    week_52_low?: number;
    week_52_high?: number;
    pe_ratio?: number;
    forward_pe?: number;
    price_to_book?: number;
    dividend_yield?: number;
    beta?: number;
  }): Promise<StockQuote | null> {
    try {
      // Round market cap to fit in BIGINT field (no decimals)
      const marketCapValue = Math.round(quoteData.market_cap || 0);

      const { data, error } = await supabase
        .from('stock_quotes')
        .insert({
          symbol: quoteData.symbol.toUpperCase(),
          price: quoteData.price,
          change_amount: quoteData.change_amount || 0,
          change_percent: quoteData.change_percent || 0,
          volume: Math.round(quoteData.volume || 0),
          market_cap: marketCapValue,
          currency: quoteData.currency || 'USD',
          week_52_low: quoteData.week_52_low || 0,
          week_52_high: quoteData.week_52_high || 0,
          pe_ratio: quoteData.pe_ratio,
          forward_pe: quoteData.forward_pe,
          price_to_book: quoteData.price_to_book,
          dividend_yield: quoteData.dividend_yield,
          beta: quoteData.beta,
          scraped_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error adding quote:', error);
        return null;
      }

      console.log(`Successfully added quote for ${quoteData.symbol}: $${quoteData.price.toFixed(2)}`);
      return data;
    } catch (error) {
      console.error('Error in addQuote:', error);
      return null;
    }
  }

  // Get popular stocks based on search history
  static async getPopularStocks(limit: number = 10): Promise<Stock[]> {
    const { data, error } = await supabase
      .from('search_history')
      .select(`
        symbol,
        search_count,
        stocks (*)
      `)
      .order('search_count', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching popular stocks:', error);
      return [];
    }

    return (data?.map(item => item.stocks).filter(Boolean) as unknown as Stock[]) || [];
  }

  // Increment search count for a stock
  static async incrementSearchCount(symbol: string): Promise<void> {
    const { error } = await supabase.rpc('increment_search_count', {
      stock_symbol: symbol.toUpperCase()
    });

    if (error) {
      console.error('Error incrementing search count:', error);
    }
  }

  // Get data freshness info
  static async getDataFreshness(symbol: string): Promise<{
    quote_age?: number;
    financials_age?: number;
    analysis_age?: number;
  }> {
    const now = new Date();
    const result: any = {};

    // Check quote age
    const quote = await this.getLatestQuote(symbol);
    if (quote) {
      result.quote_age = Math.floor((now.getTime() - new Date(quote.scraped_at).getTime()) / (1000 * 60));
    }

    // Check financials age
    const financials = await this.getLatestFinancials(symbol);
    if (financials) {
      result.financials_age = Math.floor((now.getTime() - new Date(financials.scraped_at).getTime()) / (1000 * 60));
    }

    // Check analysis age
    const analysis = await this.getLatestAnalysis(symbol);
    if (analysis) {
      result.analysis_age = Math.floor((now.getTime() - new Date(analysis.calculated_at).getTime()) / (1000 * 60));
    }

    return result;
  }
}
