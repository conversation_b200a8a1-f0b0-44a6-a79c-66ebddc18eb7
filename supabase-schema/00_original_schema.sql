-- =====================================================
-- SUPABASE DATABASE SCHEMA FOR STOCK ANALYSIS PLATFORM
-- =====================================================
-- Copy and paste this entire script into your Supabase SQL Editor
-- URL: https://fqtooantsrizxjdgtkdx.supabase.co/project/default/sql/new

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create stocks table for basic stock information
CREATE TABLE IF NOT EXISTS stocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    exchange VARCHAR(50),
    sector VARCHAR(100),
    industry VARCHAR(100),
    market_cap BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create stock_quotes table for real-time price data
CREATE TABLE IF NOT EXISTS stock_quotes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    price DECIMAL(12, 4) NOT NULL,
    change_amount DECIMAL(12, 4),
    change_percent DECIMAL(8, 4),
    volume BIGINT,
    market_cap BIGINT,
    pe_ratio DECIMAL(8, 2),
    forward_pe DECIMAL(8, 2),
    price_to_book DECIMAL(8, 2),
    dividend_yield DECIMAL(6, 4),
    beta DECIMAL(6, 3),
    week_52_low DECIMAL(12, 4),
    week_52_high DECIMAL(12, 4),
    currency VARCHAR(3) DEFAULT 'USD',
    scraped_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (symbol) REFERENCES stocks(symbol) ON DELETE CASCADE
);

-- Create stock_financials table for financial metrics
CREATE TABLE IF NOT EXISTS stock_financials (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    total_revenue BIGINT,
    total_debt BIGINT,
    total_cash BIGINT,
    free_cashflow BIGINT,
    operating_cashflow BIGINT,
    revenue_growth DECIMAL(8, 4),
    earnings_growth DECIMAL(8, 4),
    gross_margins DECIMAL(6, 4),
    operating_margins DECIMAL(6, 4),
    profit_margins DECIMAL(6, 4),
    return_on_assets DECIMAL(6, 4),
    return_on_equity DECIMAL(6, 4),
    debt_to_equity DECIMAL(8, 4),
    current_ratio DECIMAL(6, 3),
    quick_ratio DECIMAL(6, 3),
    peg_ratio DECIMAL(6, 3),
    enterprise_value BIGINT,
    book_value DECIMAL(12, 4),
    shares_outstanding BIGINT,
    scraped_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (symbol) REFERENCES stocks(symbol) ON DELETE CASCADE
);

-- Create stock_analysis table for calculated scores and recommendations
CREATE TABLE IF NOT EXISTS stock_analysis (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    overall_score DECIMAL(3, 1) NOT NULL,
    recommendation VARCHAR(10) NOT NULL CHECK (recommendation IN ('BUY', 'HOLD', 'SELL')),
    risk_level VARCHAR(10) NOT NULL CHECK (risk_level IN ('LOW', 'MEDIUM', 'HIGH')),
    category VARCHAR(20) NOT NULL CHECK (category IN ('Lower Risk', 'Balanced Risk', 'Full Throttle')),
    criteria_passed_count INTEGER NOT NULL,
    criteria_passed_percentage INTEGER NOT NULL,
    analysis_data JSONB, -- Store detailed analysis breakdown
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (symbol) REFERENCES stocks(symbol) ON DELETE CASCADE
);

-- Create search_history table for tracking popular searches
CREATE TABLE IF NOT EXISTS search_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    search_count INTEGER DEFAULT 1,
    last_searched TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (symbol) REFERENCES stocks(symbol) ON DELETE CASCADE
);

-- Create scraping_logs table for monitoring scraping activity
CREATE TABLE IF NOT EXISTS scraping_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10),
    scrape_type VARCHAR(50) NOT NULL, -- 'quote', 'financials', 'search'
    status VARCHAR(20) NOT NULL, -- 'success', 'error', 'partial'
    error_message TEXT,
    records_updated INTEGER DEFAULT 0,
    scraped_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_stocks_symbol ON stocks(symbol);
CREATE INDEX IF NOT EXISTS idx_stock_quotes_symbol ON stock_quotes(symbol);
CREATE INDEX IF NOT EXISTS idx_stock_quotes_scraped_at ON stock_quotes(scraped_at);
CREATE INDEX IF NOT EXISTS idx_stock_financials_symbol ON stock_financials(symbol);
CREATE INDEX IF NOT EXISTS idx_stock_financials_scraped_at ON stock_financials(scraped_at);
CREATE INDEX IF NOT EXISTS idx_stock_analysis_symbol ON stock_analysis(symbol);
CREATE INDEX IF NOT EXISTS idx_stock_analysis_calculated_at ON stock_analysis(calculated_at);
CREATE INDEX IF NOT EXISTS idx_search_history_symbol ON search_history(symbol);
CREATE INDEX IF NOT EXISTS idx_search_history_count ON search_history(search_count DESC);
CREATE INDEX IF NOT EXISTS idx_scraping_logs_scraped_at ON scraping_logs(scraped_at);

-- Create functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_stocks_updated_at BEFORE UPDATE ON stocks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to increment search count
CREATE OR REPLACE FUNCTION increment_search_count(stock_symbol TEXT)
RETURNS VOID AS $$
BEGIN
    INSERT INTO search_history (symbol, search_count, last_searched)
    VALUES (stock_symbol, 1, NOW())
    ON CONFLICT (symbol)
    DO UPDATE SET
        search_count = search_history.search_count + 1,
        last_searched = NOW();
END;
$$ LANGUAGE plpgsql;

-- Insert initial popular stocks
INSERT INTO stocks (symbol, name, exchange, sector, industry) VALUES
('AAPL', 'Apple Inc.', 'NASDAQ', 'Technology', 'Consumer Electronics'),
('MSFT', 'Microsoft Corporation', 'NASDAQ', 'Technology', 'Software'),
('GOOGL', 'Alphabet Inc.', 'NASDAQ', 'Technology', 'Internet Services'),
('AMZN', 'Amazon.com Inc.', 'NASDAQ', 'Consumer Discretionary', 'E-commerce'),
('NVDA', 'NVIDIA Corporation', 'NASDAQ', 'Technology', 'Semiconductors'),
('META', 'Meta Platforms Inc.', 'NASDAQ', 'Technology', 'Social Media'),
('TSLA', 'Tesla Inc.', 'NASDAQ', 'Consumer Discretionary', 'Electric Vehicles'),
('V', 'Visa Inc.', 'NYSE', 'Financial Services', 'Payment Processing'),
('JPM', 'JPMorgan Chase & Co.', 'NYSE', 'Financial Services', 'Banking'),
('JNJ', 'Johnson & Johnson', 'NYSE', 'Healthcare', 'Pharmaceuticals'),
('WMT', 'Walmart Inc.', 'NYSE', 'Consumer Staples', 'Retail'),
('PG', 'Procter & Gamble Co.', 'NYSE', 'Consumer Staples', 'Consumer Goods'),
('UNH', 'UnitedHealth Group Inc.', 'NYSE', 'Healthcare', 'Health Insurance'),
('HD', 'Home Depot Inc.', 'NYSE', 'Consumer Discretionary', 'Home Improvement'),
('MA', 'Mastercard Inc.', 'NYSE', 'Financial Services', 'Payment Processing'),
('DIS', 'Walt Disney Co.', 'NYSE', 'Communication Services', 'Entertainment'),
('PYPL', 'PayPal Holdings Inc.', 'NASDAQ', 'Financial Services', 'Payment Processing'),
('ADBE', 'Adobe Inc.', 'NASDAQ', 'Technology', 'Software'),
('NFLX', 'Netflix Inc.', 'NASDAQ', 'Communication Services', 'Streaming'),
('CRM', 'Salesforce Inc.', 'NYSE', 'Technology', 'Software'),
('AMD', 'Advanced Micro Devices Inc.', 'NASDAQ', 'Technology', 'Semiconductors'),
('INTC', 'Intel Corporation', 'NASDAQ', 'Technology', 'Semiconductors'),
('ORCL', 'Oracle Corporation', 'NYSE', 'Technology', 'Software'),
('CSCO', 'Cisco Systems Inc.', 'NASDAQ', 'Technology', 'Networking'),
('IBM', 'International Business Machines Corp.', 'NYSE', 'Technology', 'Software')
ON CONFLICT (symbol) DO NOTHING;

-- =====================================================
-- COPY THE ABOVE SQL AND PASTE IT INTO SUPABASE SQL EDITOR
-- URL: https://fqtooantsrizxjdgtkdx.supabase.co/project/default/sql/new
-- =====================================================
