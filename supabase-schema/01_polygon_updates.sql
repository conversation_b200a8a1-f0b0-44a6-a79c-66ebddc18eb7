-- =====================================================
-- POLYGON.IO INTEGRATION UPDATES
-- =====================================================
-- Copy and paste this script into your Supabase SQL Editor
-- This adds missing columns needed for Polygon.io data integration

-- Add missing columns to stocks table for Polygon.io data
ALTER TABLE stocks 
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS website VARCHAR(255),
ADD COLUMN IF NOT EXISTS employees INTEGER,
ADD COLUMN IF NOT EXISTS currency VARCHAR(10) DEFAULT 'USD',
ADD COLUMN IF NOT EXISTS country VARCHAR(10) DEFAULT 'US';

-- Update stock_quotes table to match Polygon.io data structure
-- Add missing columns that Polygon.io provides
ALTER TABLE stock_quotes 
ADD COLUMN IF NOT EXISTS high DECIMAL(12, 4),
ADD COLUMN IF NOT EXISTS low DECIMAL(12, 4),
ADD COLUMN IF NOT EXISTS open DECIMAL(12, 4),
ADD COLUMN IF NOT EXISTS previous_close DECIMAL(12, 4),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update stock_financials table for Polygon.io compatibility
-- Add updated_at column if it doesn't exist
ALTER TABLE stock_financials 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create or update scrape_activity table (renamed from scraping_logs for consistency)
CREATE TABLE IF NOT EXISTS scrape_activity (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10),
    status VARCHAR(20) NOT NULL, -- 'success', 'failed', 'partial'
    data_source VARCHAR(50) NOT NULL DEFAULT 'polygon', -- 'polygon', 'yahoo', etc.
    error_message TEXT,
    records_updated INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for new columns
CREATE INDEX IF NOT EXISTS idx_stocks_market_cap ON stocks(market_cap);
CREATE INDEX IF NOT EXISTS idx_stocks_sector ON stocks(sector);
CREATE INDEX IF NOT EXISTS idx_stock_quotes_updated_at ON stock_quotes(updated_at);
CREATE INDEX IF NOT EXISTS idx_stock_financials_updated_at ON stock_financials(updated_at);
CREATE INDEX IF NOT EXISTS idx_scrape_activity_created_at ON scrape_activity(created_at);
CREATE INDEX IF NOT EXISTS idx_scrape_activity_symbol ON scrape_activity(symbol);

-- Create trigger for stock_quotes updated_at
DROP TRIGGER IF EXISTS update_stock_quotes_updated_at ON stock_quotes;
CREATE TRIGGER update_stock_quotes_updated_at 
    BEFORE UPDATE ON stock_quotes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for stock_financials updated_at  
DROP TRIGGER IF EXISTS update_stock_financials_updated_at ON stock_financials;
CREATE TRIGGER update_stock_financials_updated_at 
    BEFORE UPDATE ON stock_financials
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for new columns
COMMENT ON COLUMN stocks.description IS 'Company business description from Polygon.io';
COMMENT ON COLUMN stocks.website IS 'Company website URL';
COMMENT ON COLUMN stocks.employees IS 'Total number of employees';
COMMENT ON COLUMN stocks.currency IS 'Trading currency (usually USD)';
COMMENT ON COLUMN stocks.country IS 'Country of incorporation';

COMMENT ON COLUMN stock_quotes.high IS 'Daily high price';
COMMENT ON COLUMN stock_quotes.low IS 'Daily low price';
COMMENT ON COLUMN stock_quotes.open IS 'Daily opening price';
COMMENT ON COLUMN stock_quotes.previous_close IS 'Previous day closing price';
COMMENT ON COLUMN stock_quotes.updated_at IS 'Last update timestamp';

COMMENT ON TABLE scrape_activity IS 'Log of data synchronization activities from external APIs';

-- Update existing stocks with default values for new columns
UPDATE stocks 
SET 
    currency = 'USD',
    country = 'US'
WHERE currency IS NULL OR country IS NULL;

-- Show current schema for verification
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('stocks', 'stock_quotes', 'stock_financials', 'scrape_activity')
ORDER BY table_name, ordinal_position;
