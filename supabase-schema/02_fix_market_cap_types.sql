-- =====================================================
-- FIX MARKET CAP DATA TYPES FOR LARGE VALUES
-- =====================================================
-- This script changes BIGINT fields to NUMERIC to handle very large market cap values
-- Run this in your Supabase SQL Editor to fix the bigint overflow errors

-- Change market_cap in stocks table from BIGINT to NUMERIC
ALTER TABLE stocks 
ALTER COLUMN market_cap TYPE NUMERIC(20, 2);

-- Change market_cap in stock_quotes table from BIGINT to NUMERIC  
ALTER TABLE stock_quotes 
ALTER COLUMN market_cap TYPE NUMERIC(20, 2);

-- Change volume in stock_quotes table from BIGINT to NUMERIC (for very high volume stocks)
ALTER TABLE stock_quotes 
ALTER COLUMN volume TYPE NUMERIC(20, 0);

-- Change other large financial fields in stock_financials table
ALTER TABLE stock_financials 
ALTER COLUMN total_revenue TYPE NUMERIC(20, 2),
ALTER COLUMN total_debt TYPE NUMERIC(20, 2),
ALTER COLUMN total_cash TYPE NUMERIC(20, 2),
ALTER COLUMN free_cashflow TYPE NUMERIC(20, 2),
ALTER COLUMN operating_cashflow TYPE NUMERIC(20, 2),
ALTER COLUMN enterprise_value TYPE NUMERIC(20, 2),
ALTER COLUMN shares_outstanding TYPE NUMERIC(20, 0);

-- Add comments explaining the change
COMMENT ON COLUMN stocks.market_cap IS 'Market capitalization in USD (NUMERIC to handle very large values)';
COMMENT ON COLUMN stock_quotes.market_cap IS 'Market capitalization in USD (NUMERIC to handle very large values)';
COMMENT ON COLUMN stock_quotes.volume IS 'Trading volume (NUMERIC to handle very high volume)';
COMMENT ON COLUMN stock_financials.total_revenue IS 'Total revenue in USD (NUMERIC for large values)';
COMMENT ON COLUMN stock_financials.total_debt IS 'Total debt in USD (NUMERIC for large values)';
COMMENT ON COLUMN stock_financials.total_cash IS 'Total cash in USD (NUMERIC for large values)';
COMMENT ON COLUMN stock_financials.free_cashflow IS 'Free cash flow in USD (NUMERIC for large values)';
COMMENT ON COLUMN stock_financials.operating_cashflow IS 'Operating cash flow in USD (NUMERIC for large values)';
COMMENT ON COLUMN stock_financials.enterprise_value IS 'Enterprise value in USD (NUMERIC for large values)';
COMMENT ON COLUMN stock_financials.shares_outstanding IS 'Shares outstanding (NUMERIC for large share counts)';

-- Show updated schema for verification
SELECT 
    table_name,
    column_name,
    data_type,
    numeric_precision,
    numeric_scale
FROM information_schema.columns 
WHERE table_name IN ('stocks', 'stock_quotes', 'stock_financials')
    AND column_name IN ('market_cap', 'volume', 'total_revenue', 'total_debt', 'total_cash', 
                       'free_cashflow', 'operating_cashflow', 'enterprise_value', 'shares_outstanding')
ORDER BY table_name, column_name;
