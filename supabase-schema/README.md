# Supabase Schema Files

This folder contains SQL files for setting up and updating your "Best Value Stock" Supabase database.

## Files

### `00_original_schema.sql`
- Your original complete database schema
- Contains all tables: stocks, stock_quotes, stock_financials, stock_analysis, search_history, scraping_logs
- Includes initial data for popular stocks

### `01_polygon_updates.sql`
- **Run this file to add Polygon.io integration support**
- Adds missing columns to existing tables:
  - `stocks`: description, website, employees, currency, country
  - `stock_quotes`: high, low, open, previous_close, updated_at
  - `stock_financials`: updated_at
- Creates indexes for better performance
- Adds triggers for automatic timestamp updates

## How to Use

1. **Copy the content of `01_polygon_updates.sql`**
2. **Go to your Supabase SQL Editor**: https://supabase.com/dashboard/project/[your-project]/sql
3. **Paste and run the SQL**
4. **Verify the updates worked** by checking the table schemas

## After Running the Updates

Your database will be ready for Polygon.io integration with these new capabilities:
- Store company descriptions and details
- Track real-time price data (high, low, open, close)
- Log API synchronization activities
- Automatic timestamp updates

## Testing

After running the SQL updates, test the sync functionality:
```bash
curl -X POST http://localhost:3000/api/polygon/sync-simple \
  -H "Content-Type: application/json" \
  -d '{"symbol":"AAPL"}'
```

This should successfully sync Apple's data from Polygon.io to your database.
